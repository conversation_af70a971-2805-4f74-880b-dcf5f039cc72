import mysql.connector
from mysql.connector import <PERSON>rro<PERSON>
from typing import Optional
from config import DB_CONFIG, AVAILABLE_DEPOTS

def get_connection() -> Optional[mysql.connector.MySQLConnection]:
    """
    Établit une connexion à la base de données MySQL
    """
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        
        if connection.is_connected():
            return connection
            
    except <PERSON>rror as e:
        print(f"Erreur de connexion à MySQL: {e}")
        return None

def create_database_and_tables():
    """
    Crée la base de données et les tables si elles n'existent pas
    """
    try:
        # Connexion sans spécifier de base de données
        db_config_no_db = DB_CONFIG.copy()
        db_config_no_db.pop('database', None)
        connection = mysql.connector.connect(**db_config_no_db)
        
        cursor = connection.cursor()
        
        # Créer la base de données
        cursor.execute("""
            CREATE DATABASE IF NOT EXISTS etat_depot_final
            DEFAULT CHARACTER SET utf8mb4
            DEFAULT COLLATE utf8mb4_general_ci
        """)
        
        # Utiliser la base de données
        cursor.execute("USE etat_depot_final")
        
        # Créer la table des dépôts
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS depots (
                id CHAR(2) PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Insérer les dépôts par défaut s'ils n'existent pas
        default_depots = list(AVAILABLE_DEPOTS.items())
        
        for depot_id, depot_nom in default_depots:
            cursor.execute("""
                INSERT IGNORE INTO depots (id, nom) 
                VALUES (%s, %s)
            """, (depot_id, depot_nom))
        
        # Créer la table des états par dépôt
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS etat_depot (
                id INT AUTO_INCREMENT PRIMARY KEY,
                depot_id CHAR(2) NOT NULL,
                unite VARCHAR(100),

                dm_essence DECIMAL(10,2) DEFAULT 0,
                dm_gasoil DECIMAL(10,2) DEFAULT 0,
                pf_essence DECIMAL(10,2) DEFAULT 0,
                pf_gasoil DECIMAL(10,2) DEFAULT 0,
                tg_essence DECIMAL(10,2) DEFAULT 0,
                tg_gasoil DECIMAL(10,2) DEFAULT 0,
                securite_essence DECIMAL(10,2) DEFAULT 0,
                securite_gasoil DECIMAL(10,2) DEFAULT 0,

                tir_inst_acc_essence DECIMAL(10,2) DEFAULT 0,
                tir_inst_acc_gasoil DECIMAL(10,2) DEFAULT 0,
                ex_nuit_acc_essence DECIMAL(10,2) DEFAULT 0,
                ex_nuit_acc_gasoil DECIMAL(10,2) DEFAULT 0,
                ex_tact_acc_essence DECIMAL(10,2) DEFAULT 0,
                ex_tact_acc_gasoil DECIMAL(10,2) DEFAULT 0,
                patrouille_acc_essence DECIMAL(10,2) DEFAULT 0,
                patrouille_acc_gasoil DECIMAL(10,2) DEFAULT 0,
                mission_acc_essence DECIMAL(10,2) DEFAULT 0,
                mission_acc_gasoil DECIMAL(10,2) DEFAULT 0,
                total_rex_acc_essence DECIMAL(10,2) DEFAULT 0,
                total_rex_acc_gasoil DECIMAL(10,2) DEFAULT 0,

                tir_inst_per_essence DECIMAL(10,2) DEFAULT 0,
                tir_inst_per_gasoil DECIMAL(10,2) DEFAULT 0,
                ex_nuit_per_essence DECIMAL(10,2) DEFAULT 0,
                ex_nuit_per_gasoil DECIMAL(10,2) DEFAULT 0,
                ex_tact_per_essence DECIMAL(10,2) DEFAULT 0,
                ex_tact_per_gasoil DECIMAL(10,2) DEFAULT 0,
                patrouille_per_essence DECIMAL(10,2) DEFAULT 0,
                patrouille_per_gasoil DECIMAL(10,2) DEFAULT 0,
                mission_per_essence DECIMAL(10,2) DEFAULT 0,
                mission_per_gasoil DECIMAL(10,2) DEFAULT 0,
                total_rex_per_essence DECIMAL(10,2) DEFAULT 0,
                total_rex_per_gasoil DECIMAL(10,2) DEFAULT 0,

                total_distribue_essence DECIMAL(10,2) DEFAULT 0,
                total_distribue_gasoil DECIMAL(10,2) DEFAULT 0,

                date_saisie DATE NOT NULL DEFAULT (CURRENT_DATE),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                FOREIGN KEY (depot_id) REFERENCES depots(id),
                UNIQUE KEY unique_depot_date_unite (depot_id, date_saisie, unite),
                INDEX idx_depot_date (depot_id, date_saisie)
            )
        """)
        
        # Créer la table des informations de dépôt
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS info_depot_final (
                id INT AUTO_INCREMENT PRIMARY KEY,
                depot_id CHAR(2) NOT NULL,
                date_saisie DATE NOT NULL,
                entre_vivo_energy_ess DECIMAL(10,2) DEFAULT 0,
                entre_vivo_energy_gas DECIMAL(10,2) DEFAULT 0,
                balance_mois_precedent_ess DECIMAL(10,2) DEFAULT 0,
                balance_mois_precedent_gas DECIMAL(10,2) DEFAULT 0,
                prise_en_compte_ess DECIMAL(10,2) DEFAULT 0,
                prise_en_compte_gas DECIMAL(10,2) DEFAULT 0,
                credit_unites_ess DECIMAL(10,2) DEFAULT 0,
                credit_unites_gas DECIMAL(10,2) DEFAULT 0,
                sortie_des_comptes_ess DECIMAL(10,2) DEFAULT 0,
                sortie_des_comptes_gas DECIMAL(10,2) DEFAULT 0,
                reste_ess DECIMAL(10,2) DEFAULT 0,
                reste_gas DECIMAL(10,2) DEFAULT 0,
                balance_mois_ess DECIMAL(10,2) DEFAULT 0,
                balance_mois_gas DECIMAL(10,2) DEFAULT 0,
                total_distributions_finales_ess DECIMAL(10,2) DEFAULT 0,
                total_distributions_finales_gas DECIMAL(10,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_depot_date (depot_id, date_saisie),
                INDEX idx_depot_date (depot_id, date_saisie),
                FOREIGN KEY (depot_id) REFERENCES depots(id)
            )
        """)
        
        connection.commit()
        print("✅ Base de données et tables créées avec succès")
        
    except Error as e:
        print(f"❌ Erreur lors de la création de la base de données: {e}")
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def get_depot_info(depot_id: str) -> Optional[tuple]:
    """
    Récupère les informations d'un dépôt
    """
    conn = get_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT id, nom FROM depots WHERE id = %s", (depot_id,))
        result = cursor.fetchone()
        return result
    except Error as e:
        print(f"Erreur lors de la récupération du dépôt {depot_id}: {e}")
        return None
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

def verify_depot_exists(depot_id: str) -> bool:
    """
    Vérifie si un dépôt existe
    """
    depot_info = get_depot_info(depot_id)
    return depot_info is not None
