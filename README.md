# Application de Gestion des Dépôts de Carburant

## Description
Application de bureau en Python avec Tkinter pour gérer l'état des dépôts de carburant. L'application permet de saisir, calculer et sauvegarder les données de distribution de carburant pour différents dépôts.

## Structure de la Base de Données

### Base de données : `etat_depot_final`

#### Table `depots`
- `id` (CHAR(2), clé primaire) : Identifiant du dépôt (D1, D2, D3, etc.)
- `nom` (VARCHAR) : Nom du dépôt
- `created_at` (TIMESTAMP) : Date de création

#### Table `etat_depot`
- `id` (INT, clé primaire) : Identifiant unique
- `depot_id` (CHAR(2)) : Référence vers le dépôt
- `unite` (VARCHAR) : Nom de l'unité
- Colonnes pour ESSENCE et GASOIL :
  - `dm_essence`, `dm_gasoil` : Dotation mensuelle
  - `pf_essence`, `pf_gasoil` : P.Fixe
  - `tg_essence`, `tg_gasoil` : Travaux génie
  - `securite_essence`, `securite_gasoil` : Sécurité
  - Colonnes pour les exercices accordés et perçus
  - `total_distribue_essence`, `total_distribue_gasoil` : Totaux distribués
- `date_saisie` (DATE) : Date de saisie
- `created_at` (TIMESTAMP) : Date de création

#### Table `info_depot_final`
- `id` (INT, clé primaire) : Identifiant unique
- `depot_id` (CHAR(2)) : Référence vers le dépôt
- `date_saisie` (DATE) : Date de saisie
- Colonnes pour les informations globales (ESSENCE et GASOIL) :
  - `entre_vivo_energy_*` : Entrées Vivo Energy
  - `balance_mois_precedent_*` : Balance mois précédent
  - `prise_en_compte_*` : Prise en compte
  - `credit_unites_*` : Crédit unités
  - `sortie_des_comptes_*` : Sortie des comptes
  - `reste_*` : Reste
  - `balance_mois_*` : Balance mois
  - `total_distributions_finales_*` : Total distributions finales

## Installation et Configuration

### Prérequis
- Python 3.7+
- MySQL Server
- Packages Python requis :
  ```bash
  pip install mysql-connector-python tkcalendar
  ```

### Configuration de la Base de Données
Modifiez les paramètres de connexion dans `db_utils.py` :
```python
connection = mysql.connector.connect(
    host='localhost',
    database='etat_depot_final',
    user='votre_utilisateur',
    password='votre_mot_de_passe',
    charset='utf8mb4',
    collation='utf8mb4_general_ci'
)
```

## Utilisation

### 1. Test de l'Application
```bash
python test_application.py
```

### 2. Lancement de l'Application
```bash
python main.py <depot_id>
```

**Exemples :**
```bash
python main.py D1    # Dépôt LAAY
python main.py D2    # Soute GAR LAAY
python main.py D3    # DEPOT.C SMARA
python main.py D4    # DEP.C AMGALA
python main.py D5    # DEP.C GUELTA
python main.py D6    # DEP.C HAOUZA
python main.py D7    # DEP.C BOUJDOUR
python main.py D8    # DEP.C BOUCRAA
```

## Fonctionnalités

### Interface Utilisateur
- **Tableau Excel-like** : Interface familière avec colonnes ESSENCE/GASOIL
- **Calculs automatiques** : Les totaux se calculent automatiquement
- **Sélection de date** : Calendrier pour choisir la date de saisie
- **Séparation par dépôt** : Chaque dépôt a ses propres données

### Calculs Automatiques
1. **Total R.EX accordés** = Somme des exercices accordés
2. **Total R.EX perçus** = Somme des exercices perçus
3. **Total distribués** = DM + PF + TG + Total R.EX perçus
4. **Reste** = ENTRE + PRISE EN COMPTE + BALANCE MOIS PRECEDENT
5. **Total distributions finales** = Total distribués + Sorties - Crédits
6. **Balance mois** = Reste - Total distributions finales

### Opérations CRUD
- **Sauvegarder** : Enregistre les données pour la date sélectionnée
- **Charger** : Charge les données pour la date sélectionnée
- **Effacer tout** : Vide tous les champs

## Architecture des Fichiers

```
📁 Projet/
├── 📄 main.py              # Point d'entrée principal
├── 📄 main_simple.py       # Interface Tkinter et logique
├── 📄 db_utils.py          # Utilitaires de base de données
├── 📄 test_application.py  # Tests de l'application
├── 📄 README.md           # Documentation
└── 📄 home.py             # Ancien fichier (peut être supprimé)
```

## Dépôts Disponibles

| ID  | Nom du Dépôt      |
|-----|-------------------|
| D1  | DEPOT LAAY        |
| D2  | SOUTE GAR LAAY    |
| D3  | DEPOT.C SMARA     |
| D4  | DEP.C AMGALA      |
| D5  | DEP.C GUELTA      |
| D6  | DEP.C HAOUZA      |
| D7  | DEP.C BOUJDOUR    |
| D8  | DEP.C BOUCRAA     |

## Dépannage

### Erreur de Connexion MySQL
- Vérifiez que MySQL Server est démarré
- Vérifiez les paramètres de connexion dans `db_utils.py`
- Assurez-vous que l'utilisateur a les droits nécessaires

### Erreur de Dépôt Inexistant
- Utilisez un des IDs de dépôt valides : D1, D2, D3, D4, D5, D6, D7, D8
- La base de données sera créée automatiquement au premier lancement

### Interface Ne Se Lance Pas
- Vérifiez que tous les packages Python sont installés
- Lancez d'abord `python test_application.py` pour diagnostiquer les problèmes

## Support
Pour toute question ou problème, vérifiez d'abord avec `python test_application.py` pour diagnostiquer les problèmes potentiels.
