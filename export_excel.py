import tkinter as tk
from tkinter import messagebox, filedialog
from datetime import date
from db_utils import get_connection, get_depot_info

def export_to_excel(root, depot_id, depot_nom, current_date=None):
    """Exporte les données du dépôt dans un fichier Excel"""
    
    # Si aucune date n'est fournie, utiliser la date actuelle
    if current_date is None:
        current_date = date.today()
    
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        
        # Demander le nom du fichier
        depot_nom_clean = depot_nom.replace(" ", "_").replace(".", "")
        default_filename = f"ETAT_DEPOT_{depot_id}_{depot_nom_clean}_{current_date.strftime('%Y%m%d')}.xlsx"
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            title=f"Exporter les données du {depot_nom} - {current_date.strftime('%d/%m/%Y')}",
            initialfile=default_filename
        )
        
        if not filename:
            return
        
        # C<PERSON>er le classeur Excel
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"{depot_id}_{current_date.strftime('%d-%m-%Y')}"
        
        # --- Styles ---
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        title_font = Font(size=16, bold=True)
        title_fill = PatternFill(start_color="FFD966", end_color="FFD966", fill_type="solid")
        border = Border(left=Side(style='thin'), right=Side(style='thin'),
                        top=Side(style='thin'), bottom=Side(style='thin'))
        
        # --- Titre ---
        ws.merge_cells('A1:R2')
        ws['A1'] = f"ETAT DU DEPOT {depot_id} - {depot_nom} - {current_date.strftime('%d/%m/%Y')}"
        ws['A1'].font = title_font
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        ws['A1'].fill = title_fill
        
        # --- En-têtes du tableau ---
        main_columns = [
            "UNITE",
            "DOTATION MENSUELLE", "P.FIXE", "TRAVAUX GENIE", "SECURITE", "Tir instruction acc",
            "Exercice de nuit acc", "Exercie.Tactique acc", "Patrouille acc", "Mission acc",
            "Total R.EX.accordes", "Tir.Inst R.ex Per", "Exercice de nuit Per", "Exercice Tactique Per",
            "Patrouille R/EX PERCUS", "Mission R/EX PERCUS", "TOTAl R/EX PERCUS", "Total distribués"
        ]
        
        sub_columns = [
            [],  # UNITE
            ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"],
            ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"],
            ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"],
            ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"]
        ]
        
        # Créer les en-têtes
        row = 4
        col = 1
        for i, main_col in enumerate(main_columns):
            if sub_columns[i]:
                start_col = col
                end_col = col + len(sub_columns[i]) - 1
                ws.merge_cells(start_row=row, start_column=start_col, end_row=row, end_column=end_col)
                cell = ws.cell(row=row, column=start_col, value=main_col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = border
                col += len(sub_columns[i])
            else:
                ws.merge_cells(start_row=row, start_column=col, end_row=row+1, end_column=col)
                cell = ws.cell(row=row, column=col, value=main_col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = border
                col += 1
        
        row = 5
        col = 1
        for i, subs in enumerate(sub_columns):
            if subs:
                for sub in subs:
                    cell = ws.cell(row=row, column=col, value=sub)
                    cell.font = Font(bold=True)
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center')
                    cell.border = border
                    col += 1
            else:
                col += 1
        
        # --- Données principales ---
        conn = get_connection()
        if not conn:
            messagebox.showerror("Erreur", "Impossible de se connecter à la base de données")
            return
        
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT unite, dm_essence, dm_gasoil, pf_essence, pf_gasoil, tg_essence, tg_gasoil,
                       securite_essence, securite_gasoil, tir_inst_acc_essence, tir_inst_acc_gasoil,
                       ex_nuit_acc_essence, ex_nuit_acc_gasoil, ex_tact_acc_essence, ex_tact_acc_gasoil,
                       patrouille_acc_essence, patrouille_acc_gasoil, mission_acc_essence, mission_acc_gasoil,
                       total_rex_acc_essence, total_rex_acc_gasoil, tir_inst_per_essence, tir_inst_per_gasoil,
                       ex_nuit_per_essence, ex_nuit_per_gasoil, ex_tact_per_essence, ex_tact_per_gasoil,
                       patrouille_per_essence, patrouille_per_gasoil, mission_per_essence, mission_per_gasoil,
                       total_rex_per_essence, total_rex_per_gasoil, total_distribue_essence, total_distribue_gasoil
                FROM etat_depot 
                WHERE depot_id = %s AND date_saisie = %s 
                ORDER BY id
            """, (depot_id, current_date))
            
            data_rows = cursor.fetchall()
            
            for row_idx, data_row in enumerate(data_rows, start=6):
                for col_idx, value in enumerate(data_row, start=1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.border = border
                    if col_idx == 1:
                        cell.font = Font(bold=True)
                    elif isinstance(value, (int, float)) and value != 0:
                        cell.alignment = Alignment(horizontal='right')
            
            # --- Données d'information ---
            cursor.execute("""
                SELECT entre_vivo_energy_ess, entre_vivo_energy_gas, balance_mois_precedent_ess, balance_mois_precedent_gas,
                       prise_en_compte_ess, prise_en_compte_gas, credit_unites_ess, credit_unites_gas,
                       sortie_des_comptes_ess, sortie_des_comptes_gas, reste_ess, reste_gas,
                       balance_mois_ess, balance_mois_gas, total_distributions_finales_ess, total_distributions_finales_gas
                FROM info_depot_final 
                WHERE depot_id = %s AND date_saisie = %s
            """, (depot_id, current_date))
            
            info_data = cursor.fetchone()
            
            if info_data:
                info_start_row = len(data_rows) + 8
                ws.merge_cells(f'A{info_start_row}:D{info_start_row}')
                ws[f'A{info_start_row}'] = "DONNÉES D'INFORMATION"
                ws[f'A{info_start_row}'].font = Font(size=14, bold=True)
                ws[f'A{info_start_row}'].fill = PatternFill(start_color="92D050", end_color="92D050", fill_type="solid")
                
                info_labels = [
                    "ENTRE VIVO ENERGY", "BALANCE MOIS PRECEDENT", "PRISE EN COMPTE", "CREDIT UNITES",
                    "SORTIE DES COMPTES", "RESTE", "BALANCE MOIS", "TOTAL DISTRIBUTIONS FINALES"
                ]
                
                info_start_row += 2
                ws[f'A{info_start_row}'] = "CATÉGORIE"
                ws[f'B{info_start_row}'] = "ESSENCE"
                ws[f'C{info_start_row}'] = "GASOIL"
                for cell in [ws[f'A{info_start_row}'], ws[f'B{info_start_row}'], ws[f'C{info_start_row}']]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.border = border
                
                for i, label in enumerate(info_labels):
                    row_num = info_start_row + 1 + i
                    ws[f'A{row_num}'] = label
                    ws[f'B{row_num}'] = info_data[i*2] if info_data[i*2] is not None else 0
                    ws[f'C{row_num}'] = info_data[i*2+1] if info_data[i*2+1] is not None else 0
                    for cell in [ws[f'A{row_num}'], ws[f'B{row_num}'], ws[f'C{row_num}']]:
                        cell.border = border
            
            # Ajuster largeurs des colonnes
            for col_num in range(1, ws.max_column + 1):
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(col_num)
                for row_num in range(1, ws.max_row + 1):
                    cell = ws.cell(row=row_num, column=col_num)
                    try:
                        if cell.value and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                ws.column_dimensions[column_letter].width = min(max_length + 2, 20)
            
            # Sauvegarder le fichier
            wb.save(filename)
            messagebox.showinfo("Succès", f"Données exportées avec succès vers:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'exportation: {str(e)}")
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
        
    except ImportError:
        messagebox.showerror("Erreur", "Le module openpyxl n'est pas installé.\nVeuillez installer avec: pip install openpyxl")
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors de l'exportation: {str(e)}")

def export_multiple_dates(root, depot_id, depot_nom):
    """Exporte les données de plusieurs dates"""
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        
        # Demander le nom du fichier
        depot_nom_clean = depot_nom.replace(" ", "_").replace(".", "")
        default_filename = f"ETAT_DEPOT_{depot_id}_{depot_nom_clean}_MULTIPLE_DATES.xlsx"
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            title=f"Exporter les données de plusieurs dates - {depot_nom}",
            initialfile=default_filename
        )
        
        if not filename:
            return
        
        # Créer le classeur Excel
        wb = openpyxl.Workbook()
        
        # Supprimer la feuille par défaut
        wb.remove(wb.active)
        
        conn = get_connection()
        if not conn:
            messagebox.showerror("Erreur", "Impossible de se connecter à la base de données")
            return
        
        try:
            cursor = conn.cursor()
            
            # Récupérer toutes les dates disponibles
            cursor.execute("""
                SELECT DISTINCT date_saisie 
                FROM etat_depot 
                WHERE depot_id = %s 
                ORDER BY date_saisie DESC
            """, (depot_id,))
            
            dates = [row[0] for row in cursor.fetchall()]
            
            if not dates:
                messagebox.showwarning("Avertissement", "Aucune donnée trouvée pour ce dépôt")
                return
            
            # Créer une feuille pour chaque date
            for date_val in dates:
                sheet_name = date_val.strftime('%d-%m-%Y')
                if len(sheet_name) > 31:  # Limite Excel
                    sheet_name = date_val.strftime('%d%m%Y')
                
                ws = wb.create_sheet(title=sheet_name)
                
                # Utiliser la fonction d'export pour cette date
                export_single_date_to_sheet(ws, depot_id, depot_nom, date_val, cursor)
            
            # Sauvegarder le fichier
            wb.save(filename)
            messagebox.showinfo("Succès", f"Données de {len(dates)} dates exportées avec succès vers:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'exportation: {str(e)}")
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
        
    except ImportError:
        messagebox.showerror("Erreur", "Le module openpyxl n'est pas installé.\nVeuillez installer avec: pip install openpyxl")
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors de l'exportation: {str(e)}")

def export_single_date_to_sheet(ws, depot_id, depot_nom, date_val, cursor):
    """Exporte les données d'une date vers une feuille Excel"""
    # Styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    title_font = Font(size=14, bold=True)
    title_fill = PatternFill(start_color="FFD966", end_color="FFD966", fill_type="solid")
    border = Border(left=Side(style='thin'), right=Side(style='thin'),
                    top=Side(style='thin'), bottom=Side(style='thin'))
    
    # Titre
    ws.merge_cells('A1:R1')
    ws['A1'] = f"ETAT DU DEPOT {depot_id} - {depot_nom} - {date_val.strftime('%d/%m/%Y')}"
    ws['A1'].font = title_font
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws['A1'].fill = title_fill
    
    # Données principales
    cursor.execute("""
        SELECT unite, dm_essence, dm_gasoil, pf_essence, pf_gasoil, tg_essence, tg_gasoil,
               securite_essence, securite_gasoil, tir_inst_acc_essence, tir_inst_acc_gasoil,
               ex_nuit_acc_essence, ex_nuit_acc_gasoil, ex_tact_acc_essence, ex_tact_acc_gasoil,
               patrouille_acc_essence, patrouille_acc_gasoil, mission_acc_essence, mission_acc_gasoil,
               total_rex_acc_essence, total_rex_acc_gasoil, tir_inst_per_essence, tir_inst_per_gasoil,
               ex_nuit_per_essence, ex_nuit_per_gasoil, ex_tact_per_essence, ex_tact_per_gasoil,
               patrouille_per_essence, patrouille_per_gasoil, mission_per_essence, mission_per_gasoil,
               total_rex_per_essence, total_rex_per_gasoil, total_distribue_essence, total_distribue_gasoil
        FROM etat_depot 
        WHERE depot_id = %s AND date_saisie = %s 
        ORDER BY id
    """, (depot_id, date_val))
    
    data_rows = cursor.fetchall()
    
    # En-têtes simplifiés
    headers = ["UNITE", "DM_ESS", "DM_GAS", "PF_ESS", "PF_GAS", "TG_ESS", "TG_GAS", 
               "SEC_ESS", "SEC_GAS", "TIR_ACC_ESS", "TIR_ACC_GAS", "NUIT_ACC_ESS", "NUIT_ACC_GAS",
               "TACT_ACC_ESS", "TACT_ACC_GAS", "PAT_ACC_ESS", "PAT_ACC_GAS", "MIS_ACC_ESS", "MIS_ACC_GAS",
               "TOT_ACC_ESS", "TOT_ACC_GAS", "TIR_PER_ESS", "TIR_PER_GAS", "NUIT_PER_ESS", "NUIT_PER_GAS",
               "TACT_PER_ESS", "TACT_PER_GAS", "PAT_PER_ESS", "PAT_PER_GAS", "MIS_PER_ESS", "MIS_PER_GAS",
               "TOT_PER_ESS", "TOT_PER_GAS", "TOT_DIST_ESS", "TOT_DIST_GAS"]
    
    # Ajouter les en-têtes
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
    
    # Ajouter les données
    for row_idx, data_row in enumerate(data_rows, start=4):
        for col_idx, value in enumerate(data_row, start=1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.border = border
            if col_idx == 1:
                cell.font = Font(bold=True)
            elif isinstance(value, (int, float)) and value != 0:
                cell.alignment = Alignment(horizontal='right')
    
    # Ajuster les largeurs
    for col_num in range(1, len(headers) + 1):
        max_length = len(headers[col_num - 1])
        column_letter = openpyxl.utils.get_column_letter(col_num)
        for row_num in range(1, ws.max_row + 1):
            cell = ws.cell(row=row_num, column=col_num)
            try:
                if cell.value and len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        ws.column_dimensions[column_letter].width = min(max_length + 2, 15)
