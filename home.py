import tkinter as tk
from tkinter import messagebox
import subprocess
import os
import sys

# --- Fonction pour lancer l'application avec l'ID du dépôt ---
def open_depot_application(depot_id):
    """Ouvre l'application main.py avec l'ID du dépôt spécifié"""
    try:
        # Chemin vers le script main.py
        script_path = os.path.join(os.path.dirname(__file__), "main.py")
        
        # Vérifier si le script existe
        if not os.path.exists(script_path):
            messagebox.showerror("Erreur", "Le fichier main.py n'existe pas.")
            return
        
        # Lancer le script avec Python et l'ID du dépôt
        subprocess.Popen([sys.executable, script_path, depot_id])
        
        # Fermer la fenêtre de lancement
        root.destroy()
        
    except Exception as e:
        messagebox.showerror("Erreur", f"Impossible d'ouvrir l'application pour {depot_id}: {str(e)}")

def on_button_hover(event, button_frame, button_text, desc_label):
    """Effet hover pour les boutons"""
    button_frame.configure(bg="#FFD700", relief="raised", bd=4)
    button_text.configure(bg="#FFD700")
    desc_label.configure(bg="#B8E6B8")

def on_button_leave(event, button_frame, button_text, desc_label):
    """Retour à l'état normal"""
    button_frame.configure(bg="#FFA500", relief="raised", bd=3)
    button_text.configure(bg="#FFA500")
    desc_label.configure(bg="#9ACD32")

# --- Création de la fenêtre principale ---
root = tk.Tk()
root.title("🛢️ APPLICATION DE GESTION CARBURANT")
root.geometry("1200x800")
root.configure(bg="#2C3E50")  # Bleu foncé moderne
root.resizable(True, True)

# --- Frame principal avec padding ---
main_frame = tk.Frame(root, bg="#2C3E50")
main_frame.pack(fill="both", expand=True, padx=30, pady=20)

# --- Titre principal ---
title_frame = tk.Frame(main_frame, bg="#34495E", relief="raised", bd=3)
title_frame.pack(fill="x", pady=(0, 30))

title_label = tk.Label(
    title_frame,
    text="🛢️ APPLICATION DE GESTION CARBURANT",
    font=("Arial", 20, "bold"),
    bg="#34495E",
    fg="#ECF0F1",
    pady=15
)
title_label.pack()

subtitle_label = tk.Label(
    title_frame,
    text="Sélectionnez un dépôt pour commencer",
    font=("Arial", 12, "italic"),
    bg="#34495E",
    fg="#BDC3C7"
)
subtitle_label.pack(pady=10)

# --- Frame pour les boutons ---
buttons_frame = tk.Frame(main_frame, bg="#2C3E50")
buttons_frame.pack(expand=True, fill="both")

# --- Données des boutons ---
button_data = [
    ("D1", "DEPOT LAAY", "#E74C3C"),
    ("D2", "SOUTE GAR LAAY", "#3498DB"),
    ("D3", "DEPOT.C SMARA", "#2ECC71"),
    ("D4", "DEP.C AMGALA", "#F39C12"),
    ("D5", "DEP.C GUELTA", "#9B59B6"),
    ("D6", "DEP.C HAOUZA", "#1ABC9C"),
    ("D7", "DEP.C BOUJDOUR", "#E67E22"),
    ("D8", "DEP.C BOUCRAA", "#34495E")
]

# Configuration de la grille pour centrer les boutons
for i in range(4):
    buttons_frame.columnconfigure(i, weight=1)
for i in range(3):
    buttons_frame.rowconfigure(i, weight=1)

# Création des boutons avec design amélioré
for i, (label, description, color) in enumerate(button_data):
    # Calculer la position (grille 4x2)
    row = i // 4
    col = i % 4
    
    # Container pour chaque bouton
    button_container = tk.Frame(buttons_frame, bg="#2C3E50")
    button_container.grid(row=row, column=col, padx=20, pady=25, sticky="nsew")
    
    # Frame principal du bouton avec ombre
    shadow_frame = tk.Frame(button_container, bg="#1A252F", height=140, width=200)
    shadow_frame.pack(padx=(3, 0), pady=(3, 0))
    
    button_frame = tk.Frame(button_container, bg=color, relief="raised", bd=2, 
                           height=140, width=200, cursor="hand2")
    button_frame.place(x=0, y=0)
    button_frame.pack_propagate(False)
    
    # Identifiant du dépôt (D1, D2, etc.)
    id_label = tk.Label(
        button_frame,
        text=label,
        font=("Arial", 24, "bold"),
        bg=color,
        fg="white",
        pady=5
    )
    id_label.pack(pady=(15, 5))
    
    # Nom du dépôt
    name_label = tk.Label(
        button_frame,
        text=description,
        font=("Arial", 11, "bold"),
        bg=color,
        fg="white",
        wraplength=180,
        justify="center"
    )
    name_label.pack(pady=(0, 10))
    
    # Indicateur de statut
    status_label = tk.Label(
        button_frame,
        text="● DISPONIBLE",
        font=("Arial", 8, "bold"),
        bg=color,
        fg="#D5DBDB"
    )
    status_label.pack(side="bottom", pady=(0, 10))
    
    # Événements
    def make_click_handler(depot_id):
        return lambda e: open_depot_application(depot_id)
    
    def make_hover_handler(frame, id_lbl, name_lbl, status_lbl, original_color):
        def on_enter(e):
            frame.configure(bg="#F8C471", relief="raised", bd=3)
            id_lbl.configure(bg="#F8C471", fg="#2C3E50")
            name_lbl.configure(bg="#F8C471", fg="#2C3E50")
            status_lbl.configure(bg="#F8C471", fg="#2C3E50", text="● CLIQUER POUR OUVRIR")
        
        def on_leave(e):
            frame.configure(bg=original_color, relief="raised", bd=2)
            id_lbl.configure(bg=original_color, fg="white")
            name_lbl.configure(bg=original_color, fg="white")
            status_lbl.configure(bg=original_color, fg="#D5DBDB", text="● DISPONIBLE")
        
        return on_enter, on_leave
    
    # Lier les événements
    click_handler = make_click_handler(label)
    hover_enter, hover_leave = make_hover_handler(button_frame, id_label, name_label, status_label, color)
    
    for widget in [button_frame, id_label, name_label, status_label]:
        widget.bind("<Button-1>", click_handler)
        widget.bind("<Enter>", hover_enter)
        widget.bind("<Leave>", hover_leave)

# --- Footer ---
footer_frame = tk.Frame(main_frame, bg="#34495E", relief="raised", bd=2)
footer_frame.pack(fill="x", pady=(30, 0))

footer_label = tk.Label(
    footer_frame,
    text="© 2024 - Système de Gestion des Carburants | Version 2.0",
    font=("Arial", 10),
    bg="#34495E",
    fg="#BDC3C7",
    pady=10
)
footer_label.pack()

# --- Bouton de sortie ---
exit_button = tk.Button(
    footer_frame,
    text="❌ QUITTER L'APPLICATION",
    font=("Arial", 12, "bold"),
    bg="#E74C3C",
    fg="white",
    relief="raised",
    bd=2,
    padx=20,
    pady=5,
    cursor="hand2",
    command=root.quit
)
exit_button.pack(pady=(0, 10))

# Effet hover pour le bouton de sortie
def on_exit_hover(e):
    exit_button.configure(bg="#C0392B")

def on_exit_leave(e):
    exit_button.configure(bg="#E74C3C")

exit_button.bind("<Enter>", on_exit_hover)
exit_button.bind("<Leave>", on_exit_leave)

# --- Lancer la boucle ---
root.mainloop()




