#!/usr/bin/env python3
"""
Fichier principal pour l'application de gestion des dépôts de carburant
Usage: python main.py <depot_id>
Exemple: python main.py D1
"""

import sys
from db_utils import (
    create_database_and_tables,
    verify_depot_exists,
    get_depot_info
)

def main():
    """Fonction principale"""
    # Vérifier les arguments
    if len(sys.argv) != 2:
        print("❌ Usage: python main.py <depot_id>")
        print("📋 Dépôts disponibles: D1, D2, D3, D4, D5, D6, D7, D8")
        print("📝 Exemple: python main.py D1")
        sys.exit(1)
    
    depot_id = sys.argv[1].upper().strip()
    
    print(f"🚀 Démarrage de l'application pour le dépôt: {depot_id}")
    
    # Créer la base de données et les tables si nécessaire
    print("🔧 Initialisation de la base de données...")
    create_database_and_tables()
    
    # Vérifier si le dépôt existe
    if not verify_depot_exists(depot_id):
        print(f"❌ Erreur: Le dépôt '{depot_id}' n'existe pas.")
        print("📋 Dépôts disponibles: D1, D2, D3, D4, D5, D6, D7, D8")
        sys.exit(1)
    
    # Récupérer les informations du dépôt
    depot_info = get_depot_info(depot_id)
    if depot_info:
        depot_nom = depot_info[1]
        print(f"✅ Dépôt trouvé: {depot_id} - {depot_nom}")
    else:
        print(f"❌ Erreur: Impossible de récupérer les informations du dépôt {depot_id}")
        sys.exit(1)
    
    # Lancer l'interface Tkinter
    print(f"🎨 Lancement de l'interface pour {depot_nom}...")
    
    try:
        # Importer et lancer main_simple avec les paramètres
        from main_simple import create_excel_style_ui
        create_excel_style_ui(depot_id, depot_nom)
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("💡 Assurez-vous que le fichier main_simple.py existe dans le même répertoire.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Erreur lors du lancement de l'interface: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
