import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
from collections import defaultdict
from db_utils import get_connection

def show_graphs(root, depot_id, depot_nom):
    """Affiche la fenêtre des graphiques avec tous les onglets"""
    graph_window = tk.Toplevel(root)
    graph_window.title(f"📊 Analyses Graphiques - {depot_nom}")
    graph_window.geometry("1400x800")
    graph_window.configure(bg="#2E2E2E")
    
    # Notebook pour les onglets
    notebook = ttk.Notebook(graph_window)
    notebook.pack(fill="both", expand=True, padx=10, pady=10)
    
    # Onglet 1: Analyse par Unité
    tab_unite = ttk.Frame(notebook)
    notebook.add(tab_unite, text="🏢 Par Unité")
    create_unite_graphs(tab_unite, depot_id)
    
    # Onglet 2: Données d'Information
    tab_info = ttk.Frame(notebook)
    notebook.add(tab_info, text="📋 Données Info")
    create_info_graphs(tab_info, depot_id)
    
    # Onglet 3: Distribution Totale
    tab_distribution = ttk.Frame(notebook)
    notebook.add(tab_distribution, text="📈 Distribution")
    create_distribution_graphs(tab_distribution, depot_id)
    
    # Onglet 4: Évolution Temporelle
    tab_evolution = ttk.Frame(notebook)
    notebook.add(tab_evolution, text="📅 Évolution")
    create_evolution_graphs(tab_evolution, depot_id)

def create_unite_graphs(parent, depot_id):
    """Crée les graphiques d'analyse par unité"""
    frame = tk.Frame(parent, bg="#2E2E2E")
    frame.pack(fill="both", expand=True)
    
    # Récupérer les données actuelles
    data_essence, data_gasoil, unites = get_current_data(depot_id)
    
    if not unites:
        tk.Label(frame, text="Aucune donnée disponible", 
                bg="#2E2E2E", fg="white", font=("Arial", 16)).pack(expand=True)
        return
    
    # Créer la figure matplotlib
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.patch.set_facecolor('#2E2E2E')
    
    # Graphique 1: Distribution Essence par Unité
    ax1.bar(unites, data_essence['total_distribue'], color='#FF6B6B', alpha=0.8)
    ax1.set_title('Distribution Essence par Unité', color='white', fontsize=12)
    ax1.set_ylabel('Litres', color='white')
    ax1.tick_params(colors='white', rotation=45)
    ax1.set_facecolor('#3E3E3E')
    
    # Graphique 2: Distribution Gasoil par Unité
    ax2.bar(unites, data_gasoil['total_distribue'], color='#4ECDC4', alpha=0.8)
    ax2.set_title('Distribution Gasoil par Unité', color='white', fontsize=12)
    ax2.set_ylabel('Litres', color='white')
    ax2.tick_params(colors='white', rotation=45)
    ax2.set_facecolor('#3E3E3E')
    
    # Graphique 3: Comparaison Essence vs Gasoil
    x = np.arange(len(unites))
    width = 0.35
    ax3.bar(x - width/2, data_essence['total_distribue'], width, label='Essence', color='#FF6B6B', alpha=0.8)
    ax3.bar(x + width/2, data_gasoil['total_distribue'], width, label='Gasoil', color='#4ECDC4', alpha=0.8)
    ax3.set_title('Comparaison Essence vs Gasoil', color='white', fontsize=12)
    ax3.set_ylabel('Litres', color='white')
    ax3.set_xticks(x)
    ax3.set_xticklabels(unites, rotation=45)
    ax3.legend()
    ax3.tick_params(colors='white')
    ax3.set_facecolor('#3E3E3E')
    
    # Graphique 4: Répartition par type d'activité (Essence)
    activites = ['DM', 'P.FIXE', 'T.GENIE', 'R/EX PERCUS']
    totaux_activites = [
        sum(data_essence['dm']),
        sum(data_essence['pf']),
        sum(data_essence['tg']),
        sum(data_essence['securite']),
        sum(data_essence['total_rex_per']),
        sum(data_essence['total_rex_acc'])
    ]
    ax4.pie(totaux_activites, labels=activites, autopct='%1.1f%%', 
            colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax4.set_title('Répartition Essence par Activité', color='white', fontsize=12)
    
    plt.tight_layout()
    
    # Intégrer dans tkinter
    canvas = FigureCanvasTkAgg(fig, frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill="both", expand=True)

def create_info_graphs(parent, depot_id):
    """Crée les graphiques d'analyse des données d'information"""
    frame = tk.Frame(parent, bg="#2E2E2E")
    frame.pack(fill="both", expand=True)
    
    # Récupérer les données
    data, dates = get_info_data_for_graphs(depot_id)
    
    if not dates:
        tk.Label(frame, text="Aucune donnée d'information disponible", 
                bg="#2E2E2E", fg="white", font=("Arial", 16)).pack(expand=True)
        return
    
    # Créer la figure avec subplots
    fig, axes = plt.subplots(2, 4, figsize=(20, 12))
    fig.patch.set_facecolor('#2E2E2E')
    fig.suptitle('Analyse des Données d\'Information par Date', fontsize=16, color='white')
    
    # Graphiques pour chaque catégorie
    categories = [
        ('ENTRE VIVO ENERGY', 'entre_vivo_ess', 'entre_vivo_gas'),
        ('BALANCE MOIS PRECEDENT', 'balance_prec_ess', 'balance_prec_gas'),
        ('PRISE EN COMPTE', 'prise_compte_ess', 'prise_compte_gas'),
        ('CREDIT UNITES', 'credit_ess', 'credit_gas'),
        ('SORTIE DES COMPTES', 'sortie_ess', 'sortie_gas'),
        ('RESTE', 'reste_ess', 'reste_gas'),
        ('BALANCE MOIS', 'balance_mois_ess', 'balance_mois_gas'),
        ('TOTAL DISTRIBUTIONS', 'total_dist_ess', 'total_dist_gas')
    ]
    
    for i, (title, ess_key, gas_key) in enumerate(categories):
        row = i // 4
        col = i % 4
        ax = axes[row, col]
        
        # Données pour le graphique
        x_pos = range(len(dates))
        width = 0.35
        
        # Barres pour essence et gasoil
        bars1 = ax.bar([x - width/2 for x in x_pos], data[ess_key], width, 
                      label='Essence', color='#FF6B6B', alpha=0.8)
        bars2 = ax.bar([x + width/2 for x in x_pos], data[gas_key], width,
                      label='Gasoil', color='#4ECDC4', alpha=0.8)
        
        # Personnalisation
        ax.set_title(title, fontsize=10, color='white', pad=10)
        ax.set_facecolor('#3E3E3E')
        ax.tick_params(colors='white', labelsize=8)
        ax.set_xticks(x_pos)
        ax.set_xticklabels([d.strftime('%d/%m') for d in dates], rotation=45)
        
        # Légende
        if i == 0:  # Seulement sur le premier graphique
            ax.legend(loc='upper left', fontsize=8)
        
        # Valeurs sur les barres
        for bar in bars1:
            height = bar.get_height()
            if height > 0:
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.0f}', ha='center', va='bottom', fontsize=7, color='white')
        
        for bar in bars2:
            height = bar.get_height()
            if height > 0:
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height:.0f}', ha='center', va='bottom', fontsize=7, color='white')
    
    plt.tight_layout()
    
    # Intégrer dans tkinter
    canvas = FigureCanvasTkAgg(fig, frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill="both", expand=True)
    
    # Barre d'outils
    toolbar = NavigationToolbar2Tk(canvas, frame)
    toolbar.update()

def create_distribution_graphs(parent, depot_id):
    """Crée les graphiques de distribution"""
    frame = tk.Frame(parent, bg="#2E2E2E")
    frame.pack(fill="both", expand=True)
    
    # Récupérer les informations de stock
    conn = get_connection()
    if not conn:
        tk.Label(frame, text="Erreur de connexion à la base de données", 
                bg="#2E2E2E", fg="white", font=("Arial", 16)).pack(expand=True)
        return
    
    try:
        cursor = conn.cursor()
        
        # Récupérer les dernières données d'info
        cursor.execute("""
            SELECT reste_ess, reste_gas, total_distributions_finales_ess, 
                   total_distributions_finales_gas, balance_mois_ess, balance_mois_gas
            FROM info_depot_final 
            WHERE depot_id = %s 
            ORDER BY date_saisie DESC LIMIT 1
        """, (depot_id,))
        
        result = cursor.fetchone()
        
        if not result:
            tk.Label(frame, text="Données de stock non disponibles", 
                    bg="#2E2E2E", fg="white", font=("Arial", 16)).pack(expand=True)
            return
        
        reste_ess, reste_gas, total_dist_fin_ess, total_dist_fin_gas, balance_ess, balance_gas = result
        
    except Exception as e:
        tk.Label(frame, text=f"Erreur: {e}", 
                bg="#2E2E2E", fg="white", font=("Arial", 16)).pack(expand=True)
        return
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.patch.set_facecolor('#2E2E2E')
    
    # Graphique 1: État des stocks Essence
    labels_ess = ['Reste', 'Distribué', 'Balance']
    values_ess = [max(0, reste_ess or 0), max(0, total_dist_fin_ess or 0), max(0, abs(balance_ess or 0))]
    colors_ess = ['#45B7D1', '#FF6B6B', '#96CEB4' if (balance_ess or 0) >= 0 else '#FF4444']
    
    # Vérifier si on a des données valides pour le graphique en secteurs
    if sum(values_ess) > 0:
        ax1.pie(values_ess, labels=labels_ess, autopct='%1.1f%%', colors=colors_ess)
    else:
        ax1.text(0.5, 0.5, 'Aucune donnée\ndisponible', ha='center', va='center', 
                transform=ax1.transAxes, color='white', fontsize=12)
    ax1.set_title('État des Stocks Essence', color='white', fontsize=12)
    
    # Graphique 2: État des stocks Gasoil
    labels_gas = ['Reste', 'Distribué', 'Balance']
    values_gas = [max(0, reste_gas or 0), max(0, total_dist_fin_gas or 0), max(0, abs(balance_gas or 0))]
    colors_gas = ['#45B7D1', '#4ECDC4', '#96CEB4' if (balance_gas or 0) >= 0 else '#FF4444']
    
    if sum(values_gas) > 0:
        ax2.pie(values_gas, labels=labels_gas, autopct='%1.1f%%', colors=colors_gas)
    else:
        ax2.text(0.5, 0.5, 'Aucune donnée\ndisponible', ha='center', va='center', 
                transform=ax2.transAxes, color='white', fontsize=12)
    ax2.set_title('État des Stocks Gasoil', color='white', fontsize=12)
    
    # Graphique 3: Comparaison des balances
    carburants = ['Essence', 'Gasoil']
    balances = [balance_ess or 0, balance_gas or 0]
    colors_balance = ['#96CEB4' if b >= 0 else '#FF4444' for b in balances]
    ax3.bar(carburants, balances, color=colors_balance, alpha=0.8)
    ax3.set_title('Balance Mensuelle par Carburant', color='white', fontsize=12)
    ax3.set_ylabel('Litres', color='white')
    ax3.axhline(y=0, color='white', linestyle='--', alpha=0.5)
    ax3.tick_params(colors='white')
    ax3.set_facecolor('#3E3E3E')
    
    # Graphique 4: Indicateurs de performance
    data_essence, data_gasoil, unites = get_current_data(depot_id)
    if unites:
        efficacite_ess = (sum(data_essence['total_rex_per']) / max(sum(data_essence['total_rex_acc']), 1)) * 100
        efficacite_gas = (sum(data_gasoil['total_rex_per']) / max(sum(data_gasoil['total_rex_acc']), 1)) * 100
        
        indicateurs = ['Efficacité\nEssence', 'Efficacité\nGasoil']
        valeurs = [efficacite_ess, efficacite_gas]
        colors_indic = ['#96CEB4' if v <= 100 else '#FF4444' for v in valeurs]
        
        ax4.bar(indicateurs, valeurs, color=colors_indic, alpha=0.8)
        ax4.set_title('Indicateurs de Performance (%)', color='white', fontsize=12)
        ax4.set_ylabel('Pourcentage', color='white')
        ax4.axhline(y=100, color='white', linestyle='--', alpha=0.5)
        ax4.tick_params(colors='white')
        ax4.set_facecolor('#3E3E3E')
    
    plt.tight_layout()
    
    canvas = FigureCanvasTkAgg(fig, frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill="both", expand=True)

def create_evolution_graphs(parent, depot_id):
    """Crée les graphiques d'évolution temporelle"""
    frame = tk.Frame(parent, bg="#2E2E2E")
    frame.pack(fill="both", expand=True)
    
    # Récupérer les données historiques
    historical_data = get_historical_data(depot_id)
    
    if not historical_data:
        tk.Label(frame, text="Données historiques non disponibles", 
                bg="#2E2E2E", fg="white", font=("Arial", 16)).pack(expand=True)
        return
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.patch.set_facecolor('#2E2E2E')
    
    dates = list(historical_data.keys())
    essence_totals = [data['essence_total'] for data in historical_data.values()]
    gasoil_totals = [data['gasoil_total'] for data in historical_data.values()]
    
    # Graphique 1: Évolution de la consommation d'essence
    ax1.plot(dates, essence_totals, marker='o', color='#FF6B6B', linewidth=2)
    ax1.set_title('Évolution Consommation Essence', color='white', fontsize=12)
    ax1.set_ylabel('Litres', color='white')
    ax1.tick_params(colors='white', rotation=45)
    ax1.set_facecolor('#3E3E3E')
    ax1.grid(True, alpha=0.3)
    
    # Graphique 2: Évolution de la consommation de gasoil
    ax2.plot(dates, gasoil_totals, marker='s', color='#4ECDC4', linewidth=2)
    ax2.set_title('Évolution Consommation Gasoil', color='white', fontsize=12)
    ax2.set_ylabel('Litres', color='white')
    ax2.tick_params(colors='white', rotation=45)
    ax2.set_facecolor('#3E3E3E')
    ax2.grid(True, alpha=0.3)
    
    # Graphique 3: Comparaison temporelle
    ax3.plot(dates, essence_totals, marker='o', label='Essence', color='#FF6B6B', linewidth=2)
    ax3.plot(dates, gasoil_totals, marker='s', label='Gasoil', color='#4ECDC4', linewidth=2)
    ax3.set_title('Comparaison Temporelle', color='white', fontsize=12)
    ax3.set_ylabel('Litres', color='white')
    ax3.legend()
    ax3.tick_params(colors='white', rotation=45)
    ax3.set_facecolor('#3E3E3E')
    ax3.grid(True, alpha=0.3)
    
    # Graphique 4: Tendance mensuelle (si assez de données)
    if len(dates) >= 7:
        # Calculer la moyenne mobile sur 7 jours
        essence_ma = np.convolve(essence_totals, np.ones(7)/7, mode='valid')
        gasoil_ma = np.convolve(gasoil_totals, np.ones(7)/7, mode='valid')
        dates_ma = dates[6:]
        
        ax4.plot(dates_ma, essence_ma, label='Essence (Moy. 7j)', color='#FF6B6B', linewidth=2)
        ax4.plot(dates_ma, gasoil_ma, label='Gasoil (Moy. 7j)', color='#4ECDC4', linewidth=2)
        ax4.set_title('Tendance (Moyenne Mobile 7 jours)', color='white', fontsize=12)
        ax4.set_ylabel('Litres', color='white')
        ax4.legend()
        ax4.tick_params(colors='white', rotation=45)
        ax4.set_facecolor('#3E3E3E')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    canvas = FigureCanvasTkAgg(fig, frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill="both", expand=True)

def get_current_data(depot_id):
    """Récupère les données actuelles du tableau"""
    conn = get_connection()
    if not conn:
        return defaultdict(list), defaultdict(list), []
    
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT unite, 
                   COALESCE(dm_essence, 0), COALESCE(dm_gasoil, 0), 
                   COALESCE(pf_essence, 0), COALESCE(pf_gasoil, 0), 
                   COALESCE(tg_essence, 0), COALESCE(tg_gasoil, 0),
                   COALESCE(total_rex_acc_essence, 0), COALESCE(total_rex_acc_gasoil, 0), 
                   COALESCE(total_rex_per_essence, 0), COALESCE(total_rex_per_gasoil, 0),
                   COALESCE(total_distribue_essence, 0), COALESCE(total_distribue_gasoil, 0)
            FROM etat_depot 
            WHERE depot_id = %s AND unite IS NOT NULL AND unite != ''
            ORDER BY date_saisie DESC, id LIMIT 30
        """, (depot_id,))
        
        results = cursor.fetchall()
        
        data_essence = defaultdict(list)
        data_gasoil = defaultdict(list)
        unites = []
        
        for row in results:
            unite = row[0]
            if not unite:
                continue
            
            unites.append(unite)
            
            # Récupérer les données pour chaque type
            data_essence['dm'].append(float(row[1]))
            data_gasoil['dm'].append(float(row[2]))
            
            data_essence['pf'].append(float(row[3]))
            data_gasoil['pf'].append(float(row[4]))
            
            data_essence['tg'].append(float(row[5]))
            data_gasoil['tg'].append(float(row[6]))
            
            data_essence['total_rex_acc'].append(float(row[7]))
            data_gasoil['total_rex_acc'].append(float(row[8]))
            
            data_essence['total_rex_per'].append(float(row[9]))
            data_gasoil['total_rex_per'].append(float(row[10]))
            
            data_essence['total_distribue'].append(float(row[11]))
            data_gasoil['total_distribue'].append(float(row[12]))
        
        return data_essence, data_gasoil, unites
        
    except Exception as e:
        print(f"Erreur récupération données actuelles: {e}")
        return defaultdict(list), defaultdict(list), []
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

def get_historical_data(depot_id):
    """Récupère les données historiques depuis la base de données"""
    conn = get_connection()
    if not conn:
        return {}
    
    try:
        cursor = conn.cursor()
        # Récupérer les 30 derniers jours avec données
        cursor.execute("""
            SELECT date_saisie, 
                   COALESCE(SUM(total_distribue_essence), 0) as essence_total,
                   COALESCE(SUM(total_distribue_gasoil), 0) as gasoil_total
            FROM etat_depot 
            WHERE depot_id = %s 
            AND date_saisie >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY date_saisie 
            HAVING essence_total > 0 OR gasoil_total > 0
            ORDER BY date_saisie
        """, (depot_id,))
        
        rows = cursor.fetchall()
        historical_data = {}
        
        for row in rows:
            date_str = row[0].strftime('%d/%m')
            historical_data[date_str] = {
                'essence_total': float(row[1] or 0),
                'gasoil_total': float(row[2] or 0)
            }
        
        # Si aucune donnée trouvée, essayer de récupérer toutes les dates disponibles
        if not historical_data:
            cursor.execute("""
                SELECT date_saisie, 
                       COALESCE(SUM(total_distribue_essence), 0) as essence_total,
                       COALESCE(SUM(total_distribue_gasoil), 0) as gasoil_total
                FROM etat_depot 
                WHERE depot_id = %s 
                GROUP BY date_saisie 
                ORDER BY date_saisie DESC
                LIMIT 10
            """, (depot_id,))
            
            rows = cursor.fetchall()
            for row in rows:
                date_str = row[0].strftime('%d/%m')
                historical_data[date_str] = {
                    'essence_total': float(row[1] or 0),
                    'gasoil_total': float(row[2] or 0)
                }
        
        return historical_data
        
    except Exception as e:
        print(f"Erreur récupération données historiques: {e}")
        return {}
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

def get_info_data_for_graphs(depot_id):
    """Récupère les données d'information pour les graphiques"""
    conn = get_connection()
    if not conn:
        return {}, []
    
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT date_saisie, 
                   entre_vivo_energy_ess, entre_vivo_energy_gas,
                   balance_mois_precedent_ess, balance_mois_precedent_gas,
                   prise_en_compte_ess, prise_en_compte_gas,
                   credit_unites_ess, credit_unites_gas,
                   sortie_des_comptes_ess, sortie_des_comptes_gas,
                   reste_ess, reste_gas,
                   balance_mois_ess, balance_mois_gas,
                   total_distributions_finales_ess, total_distributions_finales_gas
            FROM info_depot_final 
            WHERE depot_id = %s
            ORDER BY date_saisie
        """, (depot_id,))
        
        results = cursor.fetchall()
        
        data = {
            'dates': [],
            'entre_vivo_ess': [], 'entre_vivo_gas': [],
            'balance_prec_ess': [], 'balance_prec_gas': [],
            'prise_compte_ess': [], 'prise_compte_gas': [],
            'credit_ess': [], 'credit_gas': [],
            'sortie_ess': [], 'sortie_gas': [],
            'reste_ess': [], 'reste_gas': [],
            'balance_mois_ess': [], 'balance_mois_gas': [],
            'total_dist_ess': [], 'total_dist_gas': []
        }
        
        for row in results:
            data['dates'].append(row[0])
            data['entre_vivo_ess'].append(float(row[1] or 0))
            data['entre_vivo_gas'].append(float(row[2] or 0))
            data['balance_prec_ess'].append(float(row[3] or 0))
            data['balance_prec_gas'].append(float(row[4] or 0))
            data['prise_compte_ess'].append(float(row[5] or 0))
            data['prise_compte_gas'].append(float(row[6] or 0))
            data['credit_ess'].append(float(row[7] or 0))
            data['credit_gas'].append(float(row[8] or 0))
            data['sortie_ess'].append(float(row[9] or 0))
            data['sortie_gas'].append(float(row[10] or 0))
            data['reste_ess'].append(float(row[11] or 0))
            data['reste_gas'].append(float(row[12] or 0))
            data['balance_mois_ess'].append(float(row[13] or 0))
            data['balance_mois_gas'].append(float(row[14] or 0))
            data['total_dist_ess'].append(float(row[15] or 0))
            data['total_dist_gas'].append(float(row[16] or 0))
        
        return data, data['dates']
        
    except Exception as e:
        print(f"Erreur récupération données info: {e}")
        return {}, []
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()
