#!/usr/bin/env python3
"""
Script de lancement pour le tableau de bord des dépôts
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(__file__))

from dashboard import DashboardApp

def main():
    """Lance le tableau de bord"""
    print("🚀 Lancement du Tableau de Bord des Dépôts...")
    
    try:
        app = DashboardApp()
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
