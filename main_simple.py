import tkinter as tk
from tkinter import messagebox, ttk
from tkcalendar import Calendar
from datetime import date
from db_utils import get_connection, get_depot_info
from graphs import show_graphs
from export_excel import export_to_excel
import sys

# --- Variables globales ---
entries = []           # Tableau des entrées
info_entries = {}      # Champs info
root = None            # Fenêtre principale
current_date = None    # Date actuelle sélectionnée
date_label_display = None  # Label pour afficher la date
depot_id = None        # ID du dépôt courant
depot_nom = None       # Nom du dépôt courant

# --- Colonnes du tableau ---
main_columns = [
    "UNITE",
    "DOTATION MENSUELLE", "P.FIXE", "TRAVAUX GENIE", "SECURITE", "Tir instruction acc",
    "Exercice de nuit acc", "Exercie.Tactique acc", "Patrouille acc", "Mission acc",
    "Total R.EX.accordes", "Tir.Inst R.ex Per", "Exercice de nuit Per", "Exercice Tactique Per",
    "Patrouille R/EX PERCUS", "Mission R/EX PERCUS", "TOTAl R/EX PERCUS", "Total distribués"
]

sub_columns = [
    [],  # UNITE
    ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"],
    ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"],
    ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"],
    ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"], ["ESSENCE", "GASOIL"]
]

# --- Fonctions utilitaires ---
def get_column_index(col_name, sub_col="ESSENCE"):
    """
    Retourne l'index absolu de la colonne dans `entries`.
    """
    try:
        target_idx = main_columns.index(col_name)
        offset = 0
        for i in range(target_idx):
            offset += len(sub_columns[i]) if sub_columns[i] else 1
        sub_idx = sub_columns[target_idx].index(sub_col)
        return offset + sub_idx
    except (ValueError, IndexError) as e:
        print(f"Erreur d'index pour {col_name} {sub_col}: {e}")
        return -1

# --- Colonnes clés ---
ACCORDES_COLS_NAMES = [
    "Tir instruction acc",
    "Exercice de nuit acc",
    "Exercie.Tactique acc",
    "Patrouille acc",
    "Mission acc"
]

PERCUS_COLS_NAMES = [
    "Tir.Inst R.ex Per",
    "Exercice de nuit Per",
    "Exercice Tactique Per",
    "Patrouille R/EX PERCUS",
    "Mission R/EX PERCUS"
]

def show_calendar():
    """Affiche une fenêtre de calendrier pour sélectionner une date"""
    global current_date, date_label_display
    
    # Créer une fenêtre popup pour le calendrier
    cal_window = tk.Toplevel(root)
    cal_window.title("Sélectionner une date")
    cal_window.geometry("300x250")
    cal_window.resizable(False, False)
    cal_window.configure(bg="white")
    
    # Centrer la fenêtre
    cal_window.transient(root)
    cal_window.grab_set()
    
    # Créer le calendrier
    cal = Calendar(cal_window, selectmode='day', date_pattern='dd/mm/yyyy',
                   background='darkblue', foreground='white',
                   bordercolor='gray', headersbackground='navy',
                   normalbackground='lightblue', weekendbackground='lightcoral',
                   selectbackground='red', selectforeground='white')
    cal.pack(padx=10, pady=10, fill="both", expand=True)
    
    # Si une date est déjà sélectionnée, l'afficher
    if current_date:
        cal.selection_set(current_date)
    
    def on_date_select():
        global current_date
        selected_date = cal.selection_get()
        if selected_date:
            # Sauvegarder les données actuelles avant de changer
            if current_date and current_date != selected_date:
                save_row_for_date(current_date)
            
            current_date = selected_date
            # Mettre à jour l'affichage de la date
            date_label_display.config(text=f"Date: {current_date.strftime('%d/%m/%Y')}")
            
            # Charger les données pour la nouvelle date
            clear_all()
            load_data_for_date(current_date)
            calculer_info()
            
        cal_window.destroy()
    
    # Boutons
    btn_frame = tk.Frame(cal_window, bg="white")
    btn_frame.pack(pady=10)
    
    tk.Button(btn_frame, text="Sélectionner", command=on_date_select,
              bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(side="left", padx=5)
    tk.Button(btn_frame, text="Annuler", command=cal_window.destroy,
              bg="#f44336", fg="white", font=("Arial", 10, "bold")).pack(side="left", padx=5)

# --- Fonctions de calcul ---
def calculer_total_rex_acc_essence(row_entries):
    total = 0.0
    for col_name in ACCORDES_COLS_NAMES:
        idx = get_column_index(col_name, "ESSENCE")
        if idx == -1: continue
        val = row_entries[idx].get()
        try:
            total += float(val) if val else 0.0
        except ValueError:
            pass
    return round(total, 2)

def calculer_total_rex_acc_gasoil(row_entries):
    total = 0.0
    for col_name in ACCORDES_COLS_NAMES:
        idx = get_column_index(col_name, "GASOIL")
        if idx == -1: continue
        val = row_entries[idx].get()
        try:
            total += float(val) if val else 0.0
        except ValueError:
            pass
    return round(total, 2)

def calculer_total_rex_per_essence(row_entries):
    total = 0.0
    for col_name in PERCUS_COLS_NAMES:
        idx = get_column_index(col_name, "ESSENCE")
        if idx == -1: continue
        val = row_entries[idx].get()
        try:
            total += float(val) if val else 0.0
        except ValueError:
            pass
    return round(total, 2)

def calculer_total_rex_per_gasoil(row_entries):
    total = 0.0
    for col_name in PERCUS_COLS_NAMES:
        idx = get_column_index(col_name, "GASOIL")
        if idx == -1: continue
        val = row_entries[idx].get()
        try:
            total += float(val) if val else 0.0
        except ValueError:
            pass
    return round(total, 2)

def calculer_total_distribue_essence(row_entries):
    dm = float(row_entries[get_column_index("DOTATION MENSUELLE", "ESSENCE")].get() or 0)
    pf = float(row_entries[get_column_index("P.FIXE", "ESSENCE")].get() or 0)
    tg = float(row_entries[get_column_index("TRAVAUX GENIE", "ESSENCE")].get() or 0)
    securite = float(row_entries[get_column_index("SECURITE", "ESSENCE")].get() or 0)
    total_rex_acc = calculer_total_rex_acc_essence(row_entries)
    return round(dm + pf + tg + securite +total_rex_acc, 2)

def calculer_total_distribue_gasoil(row_entries):
    dm = float(row_entries[get_column_index("DOTATION MENSUELLE", "GASOIL")].get() or 0)
    pf = float(row_entries[get_column_index("P.FIXE", "GASOIL")].get() or 0)
    tg = float(row_entries[get_column_index("TRAVAUX GENIE", "GASOIL")].get() or 0)
    securite = float(row_entries[get_column_index("SECURITE", "GASOIL")].get() or 0)
    total_rex_acc = calculer_total_rex_acc_gasoil(row_entries)
    return round(dm + pf + tg + securite + total_rex_acc, 2)

def mettre_a_jour_totaux(row_entries):
    try:
        # Mise à jour de Total R.EX.accordés
        total_rex_acc_ess = calculer_total_rex_acc_essence(row_entries)
        total_rex_acc_gas = calculer_total_rex_acc_gasoil(row_entries)
        idx_ess = get_column_index("Total R.EX.accordes", "ESSENCE")
        idx_gas = get_column_index("Total R.EX.accordes", "GASOIL")
        if idx_ess != -1:
            row_entries[idx_ess].delete(0, tk.END)
            row_entries[idx_ess].insert(0, str(total_rex_acc_ess))
            row_entries[idx_ess].config(fg="green", font=("Arial", 10, "bold"))
        if idx_gas != -1:
            row_entries[idx_gas].delete(0, tk.END)
            row_entries[idx_gas].insert(0, str(total_rex_acc_gas))
            row_entries[idx_gas].config(fg="green", font=("Arial", 10, "bold"))

        # Mise à jour de TOTAl R/EX PERCUS
        total_rex_per_ess = calculer_total_rex_per_essence(row_entries)
        total_rex_per_gas = calculer_total_rex_per_gasoil(row_entries)
        idx_ess = get_column_index("TOTAl R/EX PERCUS", "ESSENCE")
        idx_gas = get_column_index("TOTAl R/EX PERCUS", "GASOIL")
        if idx_ess != -1:
            row_entries[idx_ess].delete(0, tk.END)
            row_entries[idx_ess].insert(0, str(total_rex_per_ess))
            row_entries[idx_ess].config(fg="green", font=("Arial", 10, "bold"))
        if idx_gas != -1:
            row_entries[idx_gas].delete(0, tk.END)
            row_entries[idx_gas].insert(0, str(total_rex_per_gas))
            row_entries[idx_gas].config(fg="green", font=("Arial", 10, "bold"))

        # Mise à jour de Total distribués
        total_dist_ess = calculer_total_distribue_essence(row_entries)
        total_dist_gas = calculer_total_distribue_gasoil(row_entries)
        idx_ess = get_column_index("Total distribués", "ESSENCE")
        idx_gas = get_column_index("Total distribués", "GASOIL")
        if idx_ess != -1:
            row_entries[idx_ess].delete(0, tk.END)
            row_entries[idx_ess].insert(0, str(total_dist_ess))
            row_entries[idx_ess].config(fg="green", font=("Arial", 10, "bold"))
        if idx_gas != -1:
            row_entries[idx_gas].delete(0, tk.END)
            row_entries[idx_gas].insert(0, str(total_dist_gas))
            row_entries[idx_gas].config(fg="green", font=("Arial", 10, "bold"))

        # Après la mise à jour des totaux distribués, recalculer les infos
        root.after(50, calculer_info)

    except Exception as e:
        print(f"Erreur de calcul : {e}")

def on_value_change(row_entries):
    root.after(100, lambda: mettre_a_jour_totaux(row_entries))

# --- Fonctions CRUD ---
def save_row_for_date(selected_date):
    """Sauvegarde les données pour une date spécifique"""
    conn = get_connection()
    if not conn:
        return
    try:
        cursor = conn.cursor()
        
        # Supprimer les données existantes pour cette date et ce dépôt
        cursor.execute("DELETE FROM etat_depot WHERE depot_id = %s AND date_saisie = %s", (depot_id, selected_date))
        
        # Insérer les nouvelles données - SEULEMENT les lignes avec nom d'unité
        for i, row_entries in enumerate(entries):
            unite = row_entries[0].get().strip()
            
            # IGNORER les lignes avec unité vide
            if not unite:
                continue
                
            values = []
            for j in range(1, len(row_entries)):
                try:
                    value = float(row_entries[j].get() or 0)
                    values.append(value)
                except ValueError:
                    values.append(0.0)
            
            placeholders = ', '.join(['%s'] * (len(values) + 3))
            sql = f"""
                INSERT INTO etat_depot (
                depot_id, date_saisie, unite, dm_essence, dm_gasoil, pf_essence, pf_gasoil,
                tg_essence, tg_gasoil, securite_essence, securite_gasoil,
                tir_inst_acc_essence, tir_inst_acc_gasoil, ex_nuit_acc_essence, ex_nuit_acc_gasoil,
                ex_tact_acc_essence, ex_tact_acc_gasoil, patrouille_acc_essence, patrouille_acc_gasoil,
                mission_acc_essence, mission_acc_gasoil, total_rex_acc_essence, total_rex_acc_gasoil,
                tir_inst_per_essence, tir_inst_per_gasoil, ex_nuit_per_essence, ex_nuit_per_gasoil,
                ex_tact_per_essence, ex_tact_per_gasoil, patrouille_per_essence, patrouille_per_gasoil,
                mission_per_essence, mission_per_gasoil, total_rex_per_essence, total_rex_per_gasoil,
                total_distribue_essence, total_distribue_gasoil
                ) VALUES ({placeholders})
            """
            cursor.execute(sql, [depot_id, selected_date, unite] + values)

        # SAUVEGARDER LES DONNÉES INFO
        save_info_for_date(cursor, selected_date)
        
        conn.commit()
        print(f"✅ Sauvegarde réussie pour {depot_id} - {selected_date}")
    except Exception as e:
        print(f"❌ Erreur sauvegarde : {e}")
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

def load_data_for_date(selected_date):
    """Charge les données pour une date spécifique"""
    conn = get_connection()
    if not conn:
        return
    try:
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT unite, dm_essence, dm_gasoil, pf_essence, pf_gasoil, tg_essence, tg_gasoil,
                   securite_essence, securite_gasoil, tir_inst_acc_essence, tir_inst_acc_gasoil,
                   ex_nuit_acc_essence, ex_nuit_acc_gasoil, ex_tact_acc_essence, ex_tact_acc_gasoil,
                   patrouille_acc_essence, patrouille_acc_gasoil, mission_acc_essence, mission_acc_gasoil,
                   total_rex_acc_essence, total_rex_acc_gasoil, tir_inst_per_essence, tir_inst_per_gasoil,
                   ex_nuit_per_essence, ex_nuit_per_gasoil, ex_tact_per_essence, ex_tact_per_gasoil,
                   patrouille_per_essence, patrouille_per_gasoil, mission_per_essence, mission_per_gasoil,
                   total_rex_per_essence, total_rex_per_gasoil, total_distribue_essence, total_distribue_gasoil
            FROM etat_depot WHERE depot_id = %s AND date_saisie = %s ORDER BY id
        """, (depot_id, selected_date))
        results = cursor.fetchall()

        # Vider toutes les cases d'abord
        for row in entries:
            for entry in row:
                entry.delete(0, tk.END)

        # Charger les données du tableau
        for i, result in enumerate(results):
            if i < len(entries):
                # Remplir le nom de l'unité
                unite_name = str(result[0] or "")
                entries[i][0].insert(0, unite_name)
                
                # Remplir les valeurs
                for j in range(1, len(result)):
                    if j < len(entries[i]):
                        value = result[j]
                        entries[i][j].insert(0, str(value if value is not None else 0))

        # CHARGER LES DONNÉES INFO POUR CETTE DATE
        load_info_for_date(cursor, selected_date)

    except Exception as e:
        print(f"Erreur chargement : {e}")
    finally:
        if conn and conn.is_connected():
            cursor.close()
            conn.close()

def save_info_for_date(cursor, selected_date):
    """Sauvegarde les champs info pour une date dans la table info_depot_final"""
    try:
        # Supprimer les infos existantes pour cette date et ce dépôt
        cursor.execute("DELETE FROM info_depot_final WHERE depot_id = %s AND date_saisie = %s", (depot_id, selected_date))
        
        # Mapping correct des champs interface vers base de données
        field_mapping = {
            "ENTRE VIVO ENERGY_ESS": "entre_vivo_energy_ess",
            "ENTRE VIVO ENERGY_GAS": "entre_vivo_energy_gas",
            "BALANCE MOIS PRECEDENT_ESS": "balance_mois_precedent_ess",
            "BALANCE MOIS PRECEDENT_GAS": "balance_mois_precedent_gas",
            "PRISE EN COMPTE_ESS": "prise_en_compte_ess",
            "PRISE EN COMPTE_GAS": "prise_en_compte_gas",
            "CREDIT UNITES_ESS": "credit_unites_ess",
            "CREDIT UNITES_GAS": "credit_unites_gas",
            "SORTIE DES COMPTES_ESS": "sortie_des_comptes_ess",
            "SORTIE DES COMPTES_GAS": "sortie_des_comptes_gas",
            "RESTE_ESS": "reste_ess",
            "RESTE_GAS": "reste_gas",
            "BALANCE MOIS_ESS": "balance_mois_ess",
            "BALANCE MOIS_GAS": "balance_mois_gas",
            "TOTAL DISTRIBUTIONS FINALES_ESS": "total_distributions_finales_ess",
            "TOTAL DISTRIBUTIONS FINALES_GAS": "total_distributions_finales_gas"
        }
        
        # Préparer les valeurs
        values = [depot_id, selected_date]
        for ui_key, db_key in field_mapping.items():
            try:
                value = float(info_entries[ui_key].get() or 0)
                values.append(value)
            except (ValueError, KeyError):
                values.append(0.0)
        
        # Insérer les nouvelles données
        sql = """
            INSERT INTO info_depot_final (
                depot_id, date_saisie, entre_vivo_energy_ess, entre_vivo_energy_gas,
                balance_mois_precedent_ess, balance_mois_precedent_gas,
                prise_en_compte_ess, prise_en_compte_gas,
                credit_unites_ess, credit_unites_gas,
                sortie_des_comptes_ess, sortie_des_comptes_gas,
                reste_ess, reste_gas, balance_mois_ess, balance_mois_gas,
                total_distributions_finales_ess, total_distributions_finales_gas
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(sql, values)
        print(f"✅ Données info sauvegardées pour {depot_id} - {selected_date}")
        
    except Exception as e:
        print(f"❌ Erreur sauvegarde info: {e}")

def load_info_for_date(cursor, selected_date):
    """Charge les champs info pour une date depuis la table info_depot_final"""
    try:
        cursor.execute("""
            SELECT entre_vivo_energy_ess, entre_vivo_energy_gas,
                   balance_mois_precedent_ess, balance_mois_precedent_gas,
                   prise_en_compte_ess, prise_en_compte_gas,
                   credit_unites_ess, credit_unites_gas,
                   sortie_des_comptes_ess, sortie_des_comptes_gas,
                   reste_ess, reste_gas,
                   balance_mois_ess, balance_mois_gas,
                   total_distributions_finales_ess, total_distributions_finales_gas
            FROM info_depot_final WHERE depot_id = %s AND date_saisie = %s
        """, (depot_id, selected_date))
        
        result = cursor.fetchone()
        
        # Effacer tous les champs info d'abord
        for entry in info_entries.values():
            entry.delete(0, tk.END)
        
        if result:
            db_to_ui_mapping = [
                "ENTRE VIVO ENERGY_ESS", "ENTRE VIVO ENERGY_GAS",
                "BALANCE MOIS PRECEDENT_ESS", "BALANCE MOIS PRECEDENT_GAS",
                "PRISE EN COMPTE_ESS", "PRISE EN COMPTE_GAS",
                "CREDIT UNITES_ESS", "CREDIT UNITES_GAS",
                "SORTIE DES COMPTES_ESS", "SORTIE DES COMPTES_GAS",
                "RESTE_ESS", "RESTE_GAS",
                "BALANCE MOIS_ESS", "BALANCE MOIS_GAS",
                "TOTAL DISTRIBUTIONS FINALES_ESS", "TOTAL DISTRIBUTIONS FINALES_GAS"
            ]
            
            for i, ui_key in enumerate(db_to_ui_mapping):
                if ui_key in info_entries:
                    value = result[i]
                    # Ne remplir que si la valeur n'est pas 0
                    if value and value != 0:
                        info_entries[ui_key].insert(0, str(value))
            
            print(f"✅ Données info chargées pour {depot_id} - {selected_date}")
        else:
            print(f"ℹ️ Aucune donnée info pour {depot_id} - {selected_date}")
            
    except Exception as e:
        print(f"❌ Erreur chargement info: {e}")

def clear_all():
    for row_entries in entries:
        for entry in row_entries:
            entry.delete(0, tk.END)
            entry.config(fg="white", font=("Arial", 10))
    for entry in info_entries.values():
        entry.delete(0, tk.END)

# --- Fonctions pour calculer les champs d'info ---
def calculer_info():
    """Calcule automatiquement les champs info"""
    try:
        # Fonction helper pour convertir les valeurs
        def safe_float(value):
            if isinstance(value, str):
                value = value.strip()
                if value == "" or value == "-":
                    return 0.0
            try:
                return float(value)
            except (ValueError, TypeError):
                return 0.0

        # Récupérer les valeurs saisies
        entre_ess = safe_float(info_entries["ENTRE VIVO ENERGY_ESS"].get())
        entre_gas = safe_float(info_entries["ENTRE VIVO ENERGY_GAS"].get())
        prise_ess = safe_float(info_entries["PRISE EN COMPTE_ESS"].get())
        prise_gas = safe_float(info_entries["PRISE EN COMPTE_GAS"].get())
        credit_ess = safe_float(info_entries["CREDIT UNITES_ESS"].get())
        credit_gas = safe_float(info_entries["CREDIT UNITES_GAS"].get())
        balance_mois_prec_ess = safe_float(info_entries["BALANCE MOIS PRECEDENT_ESS"].get())
        balance_mois_prec_gas = safe_float(info_entries["BALANCE MOIS PRECEDENT_GAS"].get())

        # Calcul RESTE = ENTRE + PRISE + BALANCE MOIS PRECEDENT
        reste_ess = entre_ess + prise_ess + balance_mois_prec_ess
        reste_gas = entre_gas + prise_gas + balance_mois_prec_gas
        info_entries["RESTE_ESS"].delete(0, tk.END)
        info_entries["RESTE_ESS"].insert(0, str(round(reste_ess, 2)))
        info_entries["RESTE_GAS"].delete(0, tk.END)
        info_entries["RESTE_GAS"].insert(0, str(round(reste_gas, 2)))

        # Calcul TOTAL DISTRIBUTIONS FINALES
        total_dist_ess = sum(calculer_total_distribue_essence(row) for row in entries if row[0].get().strip())
        total_dist_gas = sum(calculer_total_distribue_gasoil(row) for row in entries if row[0].get().strip())
        sortie_ess = safe_float(info_entries["SORTIE DES COMPTES_ESS"].get())
        sortie_gas = safe_float(info_entries["SORTIE DES COMPTES_GAS"].get())

        total_final_ess = total_dist_ess + sortie_ess - credit_ess
        total_final_gas = total_dist_gas + sortie_gas - credit_gas
        info_entries["TOTAL DISTRIBUTIONS FINALES_ESS"].delete(0, tk.END)
        info_entries["TOTAL DISTRIBUTIONS FINALES_ESS"].insert(0, str(round(total_final_ess, 2)))
        info_entries["TOTAL DISTRIBUTIONS FINALES_GAS"].delete(0, tk.END)
        info_entries["TOTAL DISTRIBUTIONS FINALES_GAS"].insert(0, str(round(total_final_gas, 2)))

        # Calcul BALANCE MOIS = RESTE - TOTAL DISTRIBUTIONS FINALES
        balance_ess = reste_ess - total_final_ess
        balance_gas = reste_gas - total_final_gas
        info_entries["BALANCE MOIS_ESS"].delete(0, tk.END)
        info_entries["BALANCE MOIS_ESS"].insert(0, str(round(balance_ess, 2)))
        info_entries["BALANCE MOIS_GAS"].delete(0, tk.END)
        info_entries["BALANCE MOIS_GAS"].insert(0, str(round(balance_gas, 2)))

    except Exception as e:
        print(f"Erreur calcul info : {e}")

def save_row():
    """Sauvegarde les données pour la date actuelle"""
    global current_date
    if current_date:
        save_row_for_date(current_date)
        messagebox.showinfo("Succès", f"Données sauvegardées pour {depot_nom} - {current_date.strftime('%d/%m/%Y')} !")
    else:
        messagebox.showerror("Erreur", "Aucune date sélectionnée. Veuillez choisir une date.")

def load_data():
    """Charge les données pour la date actuelle"""
    global current_date
    if current_date:
        load_data_for_date(current_date)
        messagebox.showinfo("Succès", f"Données chargées pour {depot_nom} - {current_date.strftime('%d/%m/%Y')} !")
    else:
        messagebox.showerror("Erreur", "Aucune date sélectionnée.")

def bind_cell_events(cell, row_entries):
    """Lie les événements de modification aux cellules"""
    def on_focus(event):
        canvas = cell.master.master
        if hasattr(canvas, 'canvasx'):
            x = cell.winfo_x()
            canvas_width = canvas.winfo_width()
            if x > canvas_width * 0.8:
                canvas.xview_scroll(1, "units")
            elif x < canvas_width * 0.2:
                canvas.xview_scroll(-1, "units")

    def on_enter_key(event):
        """Navigation avec la touche Entrée"""
        current_row = -1
        current_col = -1
        for row_idx, row in enumerate(entries):
            for col_idx, entry in enumerate(row):
                if entry == cell:
                    current_row = row_idx
                    current_col = col_idx
                    break
            if current_row != -1:
                break

        if current_row != -1 and current_col != -1:
            next_row = current_row
            next_col = current_col + 1
            if next_col >= len(entries[current_row]):
                next_row += 1
                next_col = 0
            if next_row >= len(entries):
                next_row = 0
                next_col = 0

            try:
                next_cell = entries[next_row][next_col]
                next_cell.focus_set()
                next_cell.icursor(tk.END)
            except (IndexError, AttributeError):
                pass

        return "break"

    cell.bind("<KeyRelease>", lambda e: on_value_change(row_entries))
    cell.bind("<FocusOut>", lambda e: on_value_change(row_entries))
    cell.bind("<FocusIn>", on_focus)
    cell.bind("<Return>", on_enter_key)
    cell.bind("<KP_Enter>", on_enter_key)

def create_excel_style_ui(depot_id_param, depot_nom_param):
    """Crée l'interface utilisateur avec le style Excel"""
    global entries, info_entries, root, current_date, date_label_display, depot_id, depot_nom
    
    depot_id = depot_id_param
    depot_nom = depot_nom_param

    num_rows = 50
    col_count = 0
    for subs in sub_columns:
        col_count += len(subs) if subs else 1

    root = tk.Tk()
    root.title(f"ETAT DU DEPOT - {depot_nom}")
    root.state('zoomed')
    root.configure(bg="#4B006E")

    # --- Titre ---
    title_frame = tk.Frame(root, bg="#C6C66E")
    title_frame.pack(fill="x", pady=(10, 0))
    tk.Label(title_frame, text=depot_id, bg="#FFB800", fg="black", font=("Arial", 18, "bold")).pack(side="left", padx=10, pady=5)
    tk.Label(title_frame, text=f"ETAT DU DEPOT - {depot_nom}", bg="#C6C66E", fg="black", font=("Arial", 18, "bold")).pack(side="left", padx=10)
    
    # Boutons en haut à droite
    buttons_right_frame = tk.Frame(title_frame, bg="#C6C66E")
    buttons_right_frame.pack(side="right", padx=10, pady=5)
    
    # Bouton Graphiques
    def refresh_and_show_graphs():
        # Rafraîchir les données avant d'afficher les graphiques
        show_graphs(root, depot_id, depot_nom)
    
    tk.Button(buttons_right_frame, text="📊 GRAPHES", bg="#4FCFFF", fg="white", 
              font=("Arial", 12, "bold"), 
              command=refresh_and_show_graphs).pack(side="left", padx=5)
    
    # Bouton Export Excel
    tk.Button(buttons_right_frame, text="📤 EXPORT EXCEL", bg="#28A745", fg="white", 
              font=("Arial", 12, "bold"), 
              command=lambda: export_to_excel(root, depot_id, depot_nom, current_date)).pack(side="left", padx=5)

    # --- Date avec bouton calendrier ---
    date_frame = tk.Frame(root, bg="#4B006E")
    date_frame.pack(anchor="w", padx=30, pady=(10, 0))
    
    # Initialiser la date actuelle
    current_date = date.today()
    
    # Label pour afficher la date
    date_label_display = tk.Label(date_frame, text=f"Date: {current_date.strftime('%d/%m/%Y')}", 
                                  bg="#4B006E", fg="white", font=("Arial", 14, "bold"))
    date_label_display.pack(side="left")
    
    # Bouton pour ouvrir le calendrier
    tk.Button(date_frame, text="📅 Choisir Date", bg="#FF9800", fg="white", 
              font=("Arial", 12, "bold"), command=show_calendar).pack(side="left", padx=10)

    # --- Tableau avec scroll ---
    container = tk.Frame(root)
    container.pack(fill="both", expand=True, padx=20, pady=10)

    canvas = tk.Canvas(container, bg="#B59B1C", height=500, width=1400, highlightthickness=0)
    h_scroll = ttk.Scrollbar(container, orient="horizontal", command=canvas.xview)
    v_scroll = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)

    scroll_frame = tk.Frame(canvas, bg="#B59B1C")

    scroll_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
    canvas.create_window((0, 0), window=scroll_frame, anchor="nw")
    canvas.configure(xscrollcommand=h_scroll.set, yscrollcommand=v_scroll.set)

    canvas.pack(side="left", fill="both", expand=True)
    h_scroll.pack(side="bottom", fill="x")
    v_scroll.pack(side="right", fill="y")

    # --- Style moderne pour les scrollbars ---
    style = ttk.Style()
    style.theme_use('clam')
    style.configure("Horizontal.TScrollbar", gripcount=0,
                    background="#C6C66E", darkcolor="#B59B1C", lightcolor="#F9D7FF",
                    troughcolor="#4B006E", bordercolor="#4B006E", arrowcolor="#4B006E")
    style.configure("Vertical.TScrollbar", gripcount=0,
                    background="#C6C66E", darkcolor="#B59B1C", lightcolor="#F9D7FF",
                    troughcolor="#4B006E", bordercolor="#4B006E", arrowcolor="#4B006E")

    # --- Entêtes ---
    col_idx = 0
    for i, col in enumerate(main_columns):
        colspan = len(sub_columns[i]) if sub_columns[i] else 1
        tk.Label(scroll_frame, text=col, bg="#B59B1C", fg="white", font=("Arial", 10, "bold"),
                 width=12, borderwidth=2, relief="ridge").grid(row=0, column=col_idx, columnspan=colspan, padx=1, pady=1, sticky="ew")
        col_idx += colspan

    col_idx = 0
    for i, subs in enumerate(sub_columns):
        if subs:
            for sub in subs:
                tk.Label(scroll_frame, text=sub, bg="#B59B1C", fg="white", font=("Arial", 9),
                         width=10, borderwidth=1, relief="ridge").grid(row=1, column=col_idx, padx=1, pady=1, sticky="ew")
                col_idx += 1
        else:
            tk.Label(scroll_frame, text="", bg="#B59B1C", width=10).grid(row=1, column=col_idx, padx=1, pady=1, sticky="ew")
            col_idx += 1

    # --- Entrées ---
    entries = []
    for row in range(num_rows):
        row_entries = []
        col_idx = 0

        # UNITE
        cell = tk.Entry(scroll_frame, width=10, bg="#B59B1C", fg="white", justify="center")
        cell.grid(row=row+2, column=col_idx, padx=1, pady=1, sticky="ew")
        row_entries.append(cell)
        col_idx += 1

        # Autres colonnes
        for i in range(1, len(main_columns)):
            for _ in sub_columns[i]:
                cell = tk.Entry(scroll_frame, width=10, bg="#B59B1C", fg="white", justify="center")
                cell.grid(row=row+2, column=col_idx, padx=1, pady=1, sticky="ew")
                row_entries.append(cell)
                col_idx += 1

        # Bind les événements après avoir rempli row_entries
        for cell in row_entries:
            bind_cell_events(cell, row_entries)
        entries.append(row_entries)

    # --- Boutons CRUD ---
    btn_frame = tk.Frame(root, bg="#4B006E")
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="💾 Sauvegarder", bg="#4CAF50", fg="white", font=("Arial", 12), command=save_row).pack(side="left", padx=10)
    tk.Button(btn_frame, text="🔁 Charger", bg="#2196F3", fg="white", font=("Arial", 12), command=load_data).pack(side="left", padx=10)
    tk.Button(btn_frame, text="🗑️ Effacer Tout", bg="#f44336", fg="white", font=("Arial", 12), command=clear_all).pack(side="left", padx=10)

    # --- Zones d'information modifiables ---
    info_frame = tk.Frame(root, bg="#4B006E", bd=2, relief="groove")
    info_frame.pack(padx=20, pady=5)

    # Champs d'info divisés
    info_labels = [
        "ENTRE VIVO ENERGY",
        "BALANCE MOIS PRECEDENT",
        "PRISE EN COMPTE",
        "CREDIT UNITES",
        "SORTIE DES COMPTES",
        "RESTE",
        "BALANCE MOIS",
        "TOTAL DISTRIBUTIONS FINALES"
    ]

    info_entries = {}

    # Affichage sur 2 lignes, 4 colonnes par ligne
    for i, label in enumerate(info_labels):
        row = 0 if i < 4 else 1
        col = i if i < 4 else i - 4
        frame = tk.Frame(info_frame, bg="#4B006E")
        frame.grid(row=row, column=col, padx=10, pady=5, sticky="w")

        tk.Label(frame, text=label, bg="#F9D7FF" if i < 4 else "#7FFFD4", fg="black",
                 font=("Arial", 10, "bold")).pack(side="left")

        entry_ess = tk.Entry(frame, width=10, justify="center", font=("Arial", 10))
        entry_ess.pack(side="left", padx=(10, 2))
        info_entries[f"{label}_ESS"] = entry_ess

        entry_gas = tk.Entry(frame, width=10, justify="center", font=("Arial", 10))
        entry_gas.pack(side="left", padx=(10, 2))
        info_entries[f"{label}_GAS"] = entry_gas

    # --- Lier les champs pour recalculer automatiquement ---
    for key, entry in info_entries.items():
        entry.bind("<KeyRelease>", lambda e: root.after(50, calculer_info))
        entry.bind("<FocusOut>", lambda e: root.after(50, calculer_info))

    # --- Charger les données pour la date actuelle ---
    load_data_for_date(current_date)
    calculer_info()

    root.mainloop()

def main():
    """Fonction principale pour initialiser l'interface"""
    # Récupérer les paramètres depuis sys.argv
    if len(sys.argv) < 2:
        print("Usage: python main_simple.py <depot_id>")
        sys.exit(1)
    
    depot_id_param = sys.argv[1].upper()
    
    # Vérifier si le dépôt existe
    depot_info = get_depot_info(depot_id_param)
    if not depot_info:
        print(f"Erreur: Le dépôt {depot_id_param} n'existe pas.")
        sys.exit(1)
    
    depot_nom_param = depot_info[1]
    
    # Lancer l'interface
    create_excel_style_ui(depot_id_param, depot_nom_param)

if __name__ == "__main__":
    main()
