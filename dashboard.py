#!/usr/bin/env python3
"""
Tableau de bord complet pour l'analyse des dépôts de carburant
Affiche les détails de tous les dépôts avec analyses et suggestions
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkcalendar import Calendar
from datetime import date, timedelta, datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
from db_utils import get_connection, get_depot_info
import numpy as np

class DashboardApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📊 DASHBOARD ANALYSE DÉPÔTS CARBURANT")
        self.root.state('zoomed')
        self.root.configure(bg="#1a1a2e")
        
        # Variables
        self.selected_date = date.today()
        self.period_type = tk.StringVar(value="jour")
        self.custom_start = None
        self.custom_end = None
        
        # Données
        self.depots_data = {}
        self.totals_data = {}
        self.analysis_data = {}
        
        self.create_interface()
        self.load_data()
    
    def create_interface(self):
        """Crée l'interface principale du tableau de bord"""
        # --- Header ---
        header_frame = tk.Frame(self.root, bg="#16213e", height=80)
        header_frame.pack(fill="x", padx=20, pady=10)
        header_frame.pack_propagate(False)
        
        # Titre principal
        title_label = tk.Label(header_frame, 
                              text="📊 DASHBOARD ANALYSE DÉPÔTS CARBURANT",
                              font=("Arial", 20, "bold"),
                              bg="#16213e", fg="#ffffff")
        title_label.pack(side="left", padx=20, pady=20)
        
        # Indicateur de période
        self.period_label = tk.Label(header_frame, 
                                   text="Période: Jour",
                                   font=("Arial", 12, "bold"),
                                   bg="#16213e", fg="#4CAF50")
        self.period_label.pack(side="left", padx=(0, 20), pady=20)
        
        # Contrôles de date et période
        controls_frame = tk.Frame(header_frame, bg="#16213e")
        controls_frame.pack(side="right", padx=20, pady=10)
        
        # Sélection de date
        tk.Label(controls_frame, text="📅 Date:", bg="#16213e", fg="#ffffff", 
                font=("Arial", 10, "bold")).pack(side="left", padx=5)
        
        self.date_btn = tk.Button(controls_frame, 
                                 text=self.selected_date.strftime("%d/%m/%Y"),
                                 command=self.select_date,
                                 bg="#4CAF50", fg="white", font=("Arial", 10, "bold"))
        self.date_btn.pack(side="left", padx=5)
        
        # Sélection de période
        tk.Label(controls_frame, text="📊 Période:", bg="#16213e", fg="#ffffff", 
                font=("Arial", 10, "bold")).pack(side="left", padx=(20,5))
        
        period_combo = ttk.Combobox(controls_frame, textvariable=self.period_type,
                                   values=["jour", "semaine", "mois", "trimestre", 
                                          "semestre", "année", "personnalisé"],
                                   state="readonly", width=12)
        period_combo.pack(side="left", padx=5)
        period_combo.bind("<<ComboboxSelected>>", self.on_period_change)
        
        # Bouton de rafraîchissement
        refresh_btn = tk.Button(controls_frame, text="🔄 Actualiser",
                               command=self.load_data,
                               bg="#2196F3", fg="white", font=("Arial", 10, "bold"))
        refresh_btn.pack(side="left", padx=(20,0))
        
        # --- Contenu principal ---
        main_frame = tk.Frame(self.root, bg="#1a1a2e")
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Notebook pour les onglets
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)
        
        # Style pour le notebook
        style = ttk.Style()
        # Configuration du thème des onglets
        style.theme_use('clam')  # Utiliser un thème de base
        
        # Style pour les onglets actifs
        style.configure("TNotebook.Tab", 
                       background="#4CAF50", 
                       foreground="white",
                       padding=[20, 10],
                       font=("Arial", 10, "bold"))
        
        # Style pour les onglets inactifs
        style.map("TNotebook.Tab",
                 background=[('selected', '#4CAF50'),
                           ('active', '#45a049'),
                           ('!selected', '#2E2E2E')],
                 foreground=[('selected', 'white'),
                           ('active', 'white'),
                           ('!selected', '#CCCCCC')])
        
        # Style pour le notebook lui-même
        style.configure("TNotebook", background="#1a1a2e", borderwidth=0)
        
        # Créer les onglets
        self.create_overview_tab()
        self.create_details_tab()
        self.create_analysis_tab()
        self.create_suggestions_tab()
    
    def create_overview_tab(self):
        """Crée l'onglet de vue d'ensemble"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📈 Vue d'ensemble")
        
        # Métriques principales
        metrics_frame = tk.Frame(overview_frame, bg="#1a1a2e")
        metrics_frame.pack(fill="x", padx=20, pady=20)
        
        # Créer les cartes de métriques
        self.create_metric_cards(metrics_frame)
        
        # Graphiques
        charts_frame = tk.Frame(overview_frame, bg="#1a1a2e")
        charts_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Graphique de distribution
        chart1_frame = tk.Frame(charts_frame, bg="#16213e", relief="raised", bd=2)
        chart1_frame.pack(side="left", fill="both", expand=True, padx=5)
        
        tk.Label(chart1_frame, text="📊 Répartition par Dépôt", 
                bg="#16213e", fg="white", font=("Arial", 12, "bold")).pack(pady=10)
        
        self.create_distribution_chart(chart1_frame)
        
        # Graphique temporel
        chart2_frame = tk.Frame(charts_frame, bg="#16213e", relief="raised", bd=2)
        chart2_frame.pack(side="right", fill="both", expand=True, padx=5)
        
        tk.Label(chart2_frame, text="📈 Évolution Temporelle", 
                bg="#16213e", fg="white", font=("Arial", 12, "bold")).pack(pady=10)
        
        self.create_temporal_chart(chart2_frame)
    
    def create_details_tab(self):
        """Crée l'onglet des détails"""
        details_frame = ttk.Frame(self.notebook)
        self.notebook.add(details_frame, text="📋 Détails par Dépôt")
        
        # Tableau des détails
        table_frame = tk.Frame(details_frame, bg="#1a1a2e")
        table_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Créer le tableau avec scrollbars
        self.create_details_table(table_frame)
    
    def create_analysis_tab(self):
        """Crée l'onglet d'analyse"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="🔍 Analyse Avancée")
        
        # Analyses statistiques
        stats_frame = tk.Frame(analysis_frame, bg="#1a1a2e")
        stats_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        self.create_analysis_charts(stats_frame)
    
    def create_suggestions_tab(self):
        """Crée l'onglet de suggestions"""
        suggestions_frame = ttk.Frame(self.notebook)
        self.notebook.add(suggestions_frame, text="💡 Suggestions")
        
        # Zone de suggestions
        suggestions_content = tk.Frame(suggestions_frame, bg="#1a1a2e")
        suggestions_content.pack(fill="both", expand=True, padx=20, pady=20)
        
        self.create_suggestions_panel(suggestions_content)
    
    def create_metric_cards(self, parent):
        """Crée les cartes de métriques principales"""
        cards_frame = tk.Frame(parent, bg="#1a1a2e")
        cards_frame.pack(fill="x")
        
        # Métriques principales
        metrics = [
            ("🏭", "Total Dépôts", "8", "#4CAF50"),
            ("⛽", "Total Essence", "0 L", "#FF9800"),
            ("🛢️", "Total Gasoil", "0 L", "#2196F3"),
            ("💰", "Valeur Stock", "0 €", "#9C27B0"),
            ("📊", "Efficacité Moy.", "0%", "#F44336")
        ]
        
        for i, (icon, label, value, color) in enumerate(metrics):
            card = tk.Frame(cards_frame, bg=color, relief="raised", bd=2)
            card.pack(side="left", fill="both", expand=True, padx=5)
            
            # Icône
            tk.Label(card, text=icon, bg=color, fg="white", 
                    font=("Arial", 20)).pack(pady=(10,5))
            
            # Valeur
            tk.Label(card, text=value, bg=color, fg="white", 
                    font=("Arial", 16, "bold")).pack()
            
            # Label
            tk.Label(card, text=label, bg=color, fg="white", 
                    font=("Arial", 10)).pack(pady=(0,10))
            
            # Stocker la référence pour mise à jour
            setattr(self, f"metric_{i}_label", card.winfo_children()[1])
    
    def create_details_table(self, parent):
        """Crée le tableau détaillé des dépôts"""
        # Frame pour le tableau avec scrollbars
        table_container = tk.Frame(parent, bg="#1a1a2e")
        table_container.pack(fill="both", expand=True)
        
        # Créer le Treeview
        columns = [
            "Dépôt", "Entre Vivo Ess", "Entre Vivo Gas", "Balance Prec Ess", "Balance Prec Gas",
            "Prise Compte Ess", "Prise Compte Gas", "Crédit Unités Ess", "Crédit Unités Gas",
            "Sortie Ess", "Sortie Gas", "Reste Ess", "Reste Gas",
            "Balance Mois Ess", "Balance Mois Gas", "Total Dist Ess", "Total Dist Gas"
        ]
        
        self.tree = ttk.Treeview(table_container, columns=columns, show="headings", height=15)
        
        # Configurer les colonnes
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, anchor="center")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Placement
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)
        
        # Ligne des totaux
        totals_frame = tk.Frame(parent, bg="#16213e", height=50)
        totals_frame.pack(fill="x", pady=(10,0))
        totals_frame.pack_propagate(False)
        
        tk.Label(totals_frame, text="📊 TOTAUX GÉNÉRAUX", 
                bg="#16213e", fg="white", font=("Arial", 12, "bold")).pack(pady=15)
    
    def create_distribution_chart(self, parent):
        """Crée le graphique de répartition"""
        try:
            fig, ax = plt.subplots(figsize=(6, 4))
            fig.patch.set_facecolor('#16213e')
            ax.set_facecolor('#16213e')
            
            # Données de test
            depots = ['D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8']
            values = [100, 80, 120, 90, 110, 95, 85, 105]
            
            bars = ax.bar(depots, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', 
                                               '#FECA57', '#FF9FF3', '#54A0FF', '#5F27CD'])
            
            ax.set_title('Répartition par Dépôt', color='white', fontsize=12)
            ax.set_ylabel('Litres', color='white')
            ax.tick_params(colors='white')
            
            # Valeurs sur les barres
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{height}', ha='center', va='bottom', color='white')
            
            plt.tight_layout()
            
            # Intégrer dans tkinter
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)
            
        except Exception as e:
            tk.Label(parent, text=f"Erreur graphique: {e}", 
                    bg="#16213e", fg="white").pack(pady=50)
    
    def create_temporal_chart(self, parent):
        """Crée le graphique temporel"""
        try:
            fig, ax = plt.subplots(figsize=(6, 4))
            fig.patch.set_facecolor('#16213e')
            ax.set_facecolor('#16213e')
            
            # Données de test
            dates = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun']
            essence = [100, 120, 110, 130, 125, 140]
            gasoil = [80, 90, 85, 95, 100, 110]
            
            ax.plot(dates, essence, marker='o', label='Essence', color='#FF6B6B', linewidth=2)
            ax.plot(dates, gasoil, marker='s', label='Gasoil', color='#4ECDC4', linewidth=2)
            
            ax.set_title('Évolution Mensuelle', color='white', fontsize=12)
            ax.set_ylabel('Litres', color='white')
            ax.legend()
            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # Intégrer dans tkinter
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)
            
        except Exception as e:
            tk.Label(parent, text=f"Erreur graphique: {e}", 
                    bg="#16213e", fg="white").pack(pady=50)
    
    def create_analysis_charts(self, parent):
        """Crée les graphiques d'analyse"""
        # Graphiques d'analyse avancée
        analysis_frame = tk.Frame(parent, bg="#1a1a2e")
        analysis_frame.pack(fill="both", expand=True)
        
        tk.Label(analysis_frame, text="🔍 Analyses Statistiques Avancées", 
                bg="#1a1a2e", fg="white", font=("Arial", 16, "bold")).pack(pady=20)
        
        # Placeholder pour analyses futures
        tk.Label(analysis_frame, text="Analyses en cours de développement...", 
                bg="#1a1a2e", fg="white", font=("Arial", 12)).pack(pady=50)
    
    def create_suggestions_panel(self, parent):
        """Crée le panneau de suggestions"""
        tk.Label(parent, text="💡 SUGGESTIONS D'AMÉLIORATION", 
                bg="#1a1a2e", fg="white", font=("Arial", 16, "bold")).pack(pady=20)
        
        suggestions_frame = tk.Frame(parent, bg="#1a1a2e")
        suggestions_frame.pack(fill="both", expand=True)
        
        # Suggestions par catégorie
        suggestions = [
            ("🔋 Optimisation Stock", [
                "• Surveiller les niveaux de stock critiques",
                "• Optimiser les rotations de stock",
                "• Prévoir les besoins saisonniers"
            ]),
            ("📊 Analyses Avancées", [
                "• Corrélations entre consommation et activité",
                "• Prévisions basées sur l'historique",
                "• Alertes automatiques de seuils"
            ]),
            ("⚡ Efficacité Opérationnelle", [
                "• Automatisation des rapports",
                "• Intégration avec systèmes externes",
                "• Formation des utilisateurs"
            ]),
            ("🛡️ Sécurité & Contrôle", [
                "• Audit des mouvements de stock",
                "• Contrôles d'accès renforcés",
                "• Traçabilité complète"
            ])
        ]
        
        for i, (category, items) in enumerate(suggestions):
            cat_frame = tk.Frame(suggestions_frame, bg="#16213e", relief="raised", bd=2)
            cat_frame.pack(fill="x", padx=20, pady=10)
            
            # Titre de catégorie
            tk.Label(cat_frame, text=category, bg="#16213e", fg="white", 
                    font=("Arial", 12, "bold")).pack(pady=10)
            
            # Items de suggestion
            for item in items:
                tk.Label(cat_frame, text=item, bg="#16213e", fg="white", 
                        font=("Arial", 10), anchor="w").pack(padx=20, pady=2)
    
    def select_date(self):
        """Ouvre le calendrier pour sélectionner une date"""
        cal_window = tk.Toplevel(self.root)
        cal_window.title("Sélectionner une date")
        cal_window.geometry("300x300")
        cal_window.configure(bg="#1a1a2e")
        
        cal = Calendar(cal_window, selectmode='day', 
                      date_pattern='dd/mm/yyyy',
                      background='#16213e', foreground='white',
                      headersbackground='#4CAF50', headersforeground='white',
                      selectbackground='#FF9800', selectforeground='white')
        cal.pack(pady=20)
        
        def on_date_select():
            # Le calendrier retourne une chaîne, on doit la convertir en date
            date_str = cal.get_date()
            try:
                # Convertir la chaîne en objet date
                self.selected_date = datetime.strptime(date_str, '%d/%m/%Y').date()
                self.date_btn.config(text=self.selected_date.strftime("%d/%m/%Y"))
                cal_window.destroy()
                self.load_data()
            except ValueError:
                messagebox.showerror("Erreur", "Format de date invalide")
        
        tk.Button(cal_window, text="Valider", command=on_date_select,
                 bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(pady=10)
    
    def on_period_change(self, event=None):
        """Gère le changement de période"""
        period = self.period_type.get()
        if period == "personnalisé":
            self.select_custom_period()
        else:
            self.load_data()
    
    def select_custom_period(self):
        """Sélectionne une période personnalisée"""
        # Interface pour sélectionner période personnalisée
        period_window = tk.Toplevel(self.root)
        period_window.title("Période personnalisée")
        period_window.geometry("400x300")
        period_window.configure(bg="#1a1a2e")
        
        tk.Label(period_window, text="Sélectionner la période", 
                bg="#1a1a2e", fg="white", font=("Arial", 14, "bold")).pack(pady=20)
        
        # Date de début
        tk.Label(period_window, text="Date de début:", 
                bg="#1a1a2e", fg="white").pack(pady=5)
        
        start_frame = tk.Frame(period_window, bg="#1a1a2e")
        start_frame.pack(pady=5)
        
        start_cal = Calendar(start_frame, selectmode='day', 
                           date_pattern='dd/mm/yyyy',
                           background='#16213e', foreground='white')
        start_cal.pack()
        
        # Date de fin
        tk.Label(period_window, text="Date de fin:", 
                bg="#1a1a2e", fg="white").pack(pady=5)
        
        end_frame = tk.Frame(period_window, bg="#1a1a2e")
        end_frame.pack(pady=5)
        
        end_cal = Calendar(end_frame, selectmode='day', 
                         date_pattern='dd/mm/yyyy',
                         background='#16213e', foreground='white')
        end_cal.pack()
        
        def validate_period():
            try:
                # Convertir les chaînes en objets date
                start_str = start_cal.get_date()
                end_str = end_cal.get_date()
                self.custom_start = datetime.strptime(start_str, '%d/%m/%Y').date()
                self.custom_end = datetime.strptime(end_str, '%d/%m/%Y').date()
                period_window.destroy()
                self.load_data()
            except ValueError:
                messagebox.showerror("Erreur", "Format de date invalide")
        
        tk.Button(period_window, text="Valider", command=validate_period,
                 bg="#4CAF50", fg="white", font=("Arial", 10, "bold")).pack(pady=20)
    
    def load_data(self):
        """Charge les données depuis la base de données"""
        try:
            conn = get_connection()
            if not conn:
                messagebox.showerror("Erreur", "Impossible de se connecter à la base de données")
                return
            
            cursor = conn.cursor()
            
            # Déterminer la période de calcul
            start_date, end_date = self.get_period_dates()
            
            # Charger les données pour chaque dépôt
            depots = ["D1", "D2", "D3", "D4", "D5", "D6", "D7", "D8"]
            
            for depot_id in depots:
                depot_info = get_depot_info(depot_id)
                if depot_info:
                    depot_name = depot_info[1]
                    
                    # Récupérer les données d'information avec SUM pour la période
                    cursor.execute("""
                        SELECT 
                            COALESCE(SUM(entre_vivo_energy_ess), 0) as entre_vivo_ess,
                            COALESCE(SUM(entre_vivo_energy_gas), 0) as entre_vivo_gas,
                            COALESCE(SUM(balance_mois_precedent_ess), 0) as balance_prec_ess,
                            COALESCE(SUM(balance_mois_precedent_gas), 0) as balance_prec_gas,
                            COALESCE(SUM(prise_en_compte_ess), 0) as prise_compte_ess,
                            COALESCE(SUM(prise_en_compte_gas), 0) as prise_compte_gas,
                            COALESCE(SUM(credit_unites_ess), 0) as credit_ess,
                            COALESCE(SUM(credit_unites_gas), 0) as credit_gas,
                            COALESCE(SUM(sortie_des_comptes_ess), 0) as sortie_ess,
                            COALESCE(SUM(sortie_des_comptes_gas), 0) as sortie_gas,
                            COALESCE(SUM(reste_ess), 0) as reste_ess,
                            COALESCE(SUM(reste_gas), 0) as reste_gas,
                            COALESCE(SUM(balance_mois_ess), 0) as balance_mois_ess,
                            COALESCE(SUM(balance_mois_gas), 0) as balance_mois_gas,
                            COALESCE(SUM(total_distributions_finales_ess), 0) as total_dist_ess,
                            COALESCE(SUM(total_distributions_finales_gas), 0) as total_dist_gas
                        FROM info_depot_final 
                        WHERE depot_id = %s AND date_saisie BETWEEN %s AND %s
                    """, (depot_id, start_date, end_date))
                    
                    data = cursor.fetchone()
                    # S'assurer que tous les résultats sont lus
                    cursor.fetchall()
                    
                    if data:
                        self.depots_data[depot_id] = {
                            'nom': depot_name,
                            'entre_vivo_ess': float(data[0] or 0),
                            'entre_vivo_gas': float(data[1] or 0),
                            'balance_prec_ess': float(data[2] or 0),
                            'balance_prec_gas': float(data[3] or 0),
                            'prise_compte_ess': float(data[4] or 0),
                            'prise_compte_gas': float(data[5] or 0),
                            'credit_ess': float(data[6] or 0),
                            'credit_gas': float(data[7] or 0),
                            'sortie_ess': float(data[8] or 0),
                            'sortie_gas': float(data[9] or 0),
                            'reste_ess': float(data[10] or 0),
                            'reste_gas': float(data[11] or 0),
                            'balance_mois_ess': float(data[12] or 0),
                            'balance_mois_gas': float(data[13] or 0),
                            'total_dist_ess': float(data[14] or 0),
                            'total_dist_gas': float(data[15] or 0)
                        }
            
            # Calculer les totaux
            self.calculate_totals()
            
            # Mettre à jour l'interface
            self.update_interface()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")
        finally:
            if conn and conn.is_connected():
                cursor.close()
                conn.close()
    
    def get_period_dates(self):
        """Calcule les dates de début et fin selon la période sélectionnée"""
        period = self.period_type.get()
        
        if period == "jour":
            return self.selected_date, self.selected_date
        elif period == "semaine":
            # Semaine du lundi au dimanche de la date sélectionnée
            start = self.selected_date - timedelta(days=self.selected_date.weekday())
            end = start + timedelta(days=6)
            return start, end
        elif period == "mois":
            # Mois complet de la date sélectionnée (1er au dernier jour)
            start = self.selected_date.replace(day=1)
            if start.month == 12:
                end = start.replace(year=start.year + 1, month=1) - timedelta(days=1)
            else:
                end = start.replace(month=start.month + 1) - timedelta(days=1)
            return start, end
        elif period == "trimestre":
            # Trimestre de la date sélectionnée
            quarter = (self.selected_date.month - 1) // 3 + 1
            start = self.selected_date.replace(month=(quarter - 1) * 3 + 1, day=1)
            if quarter == 4:
                end = start.replace(year=start.year + 1, month=1) - timedelta(days=1)
            else:
                end = start.replace(month=quarter * 3 + 1) - timedelta(days=1)
            return start, end
        elif period == "semestre":
            # Semestre de la date sélectionnée
            if self.selected_date.month <= 6:
                start = self.selected_date.replace(month=1, day=1)
                end = self.selected_date.replace(month=6, day=30)
            else:
                start = self.selected_date.replace(month=7, day=1)
                end = self.selected_date.replace(month=12, day=31)
            return start, end
        elif period == "année":
            # Année complète de la date sélectionnée
            start = self.selected_date.replace(month=1, day=1)
            end = self.selected_date.replace(month=12, day=31)
            return start, end
        elif period == "personnalisé":
            return self.custom_start or self.selected_date, self.custom_end or self.selected_date
        else:
            return self.selected_date, self.selected_date
    
    def calculate_totals(self):
        """Calcule les totaux généraux"""
        self.totals_data = {
            'entre_vivo_ess': sum(data['entre_vivo_ess'] for data in self.depots_data.values()),
            'entre_vivo_gas': sum(data['entre_vivo_gas'] for data in self.depots_data.values()),
            'balance_prec_ess': sum(data['balance_prec_ess'] for data in self.depots_data.values()),
            'balance_prec_gas': sum(data['balance_prec_gas'] for data in self.depots_data.values()),
            'prise_compte_ess': sum(data['prise_compte_ess'] for data in self.depots_data.values()),
            'prise_compte_gas': sum(data['prise_compte_gas'] for data in self.depots_data.values()),
            'credit_ess': sum(data['credit_ess'] for data in self.depots_data.values()),
            'credit_gas': sum(data['credit_gas'] for data in self.depots_data.values()),
            'sortie_ess': sum(data['sortie_ess'] for data in self.depots_data.values()),
            'sortie_gas': sum(data['sortie_gas'] for data in self.depots_data.values()),
            'reste_ess': sum(data['reste_ess'] for data in self.depots_data.values()),
            'reste_gas': sum(data['reste_gas'] for data in self.depots_data.values()),
            'balance_mois_ess': sum(data['balance_mois_ess'] for data in self.depots_data.values()),
            'balance_mois_gas': sum(data['balance_mois_gas'] for data in self.depots_data.values()),
            'total_dist_ess': sum(data['total_dist_ess'] for data in self.depots_data.values()),
            'total_dist_gas': sum(data['total_dist_gas'] for data in self.depots_data.values())
        }
        
        # Calculer les moyennes
        num_depots = len(self.depots_data)
        if num_depots > 0:
            self.analysis_data = {
                'moyenne_ess': self.totals_data['total_dist_ess'] / num_depots,
                'moyenne_gas': self.totals_data['total_dist_gas'] / num_depots,
                'efficacite_moyenne': 85.0  # Placeholder
            }
    
    def update_interface(self):
        """Met à jour l'interface avec les nouvelles données"""
        # Mettre à jour l'indicateur de période
        if hasattr(self, 'period_label'):
            period = self.period_type.get()
            start_date, end_date = self.get_period_dates()
            
            if period == "jour":
                period_text = f"Période: Jour ({self.selected_date.strftime('%d/%m/%Y')})"
            elif period == "semaine":
                period_text = f"Période: Semaine ({start_date.strftime('%d/%m')} - {end_date.strftime('%d/%m/%Y')})"
            elif period == "mois":
                period_text = f"Période: Mois ({start_date.strftime('%m/%Y')})"
            elif period == "trimestre":
                quarter = (self.selected_date.month - 1) // 3 + 1
                period_text = f"Période: Trimestre Q{quarter} ({start_date.strftime('%m/%Y')} - {end_date.strftime('%m/%Y')})"
            elif period == "semestre":
                sem = "1er" if start_date.month <= 6 else "2ème"
                period_text = f"Période: {sem} Semestre {start_date.year}"
            elif period == "année":
                period_text = f"Période: Année {start_date.year}"
            elif period == "personnalisé":
                period_text = f"Période: Personnalisée ({start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')})"
            else:
                period_text = "Période: Non définie"
            
            self.period_label.config(text=period_text)
        
        # Mettre à jour les métriques
        if hasattr(self, 'metric_1_label'):
            self.metric_1_label.config(text=f"{self.totals_data['total_dist_ess']:.0f} L")
        if hasattr(self, 'metric_2_label'):
            self.metric_2_label.config(text=f"{self.totals_data['total_dist_gas']:.0f} L")
        if hasattr(self, 'metric_4_label'):
            self.metric_4_label.config(text=f"{self.analysis_data.get('efficacite_moyenne', 0):.1f}%")
        
        # Mettre à jour le tableau
        if hasattr(self, 'tree'):
            # Vider le tableau
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Ajouter les données des dépôts
            for depot_id, data in self.depots_data.items():
                values = [
                    f"{depot_id} - {data['nom']}",
                    f"{data['entre_vivo_ess']:.0f}",
                    f"{data['entre_vivo_gas']:.0f}",
                    f"{data['balance_prec_ess']:.0f}",
                    f"{data['balance_prec_gas']:.0f}",
                    f"{data['prise_compte_ess']:.0f}",
                    f"{data['prise_compte_gas']:.0f}",
                    f"{data['credit_ess']:.0f}",
                    f"{data['credit_gas']:.0f}",
                    f"{data['sortie_ess']:.0f}",
                    f"{data['sortie_gas']:.0f}",
                    f"{data['reste_ess']:.0f}",
                    f"{data['reste_gas']:.0f}",
                    f"{data['balance_mois_ess']:.0f}",
                    f"{data['balance_mois_gas']:.0f}",
                    f"{data['total_dist_ess']:.0f}",
                    f"{data['total_dist_gas']:.0f}"
                ]
                self.tree.insert("", "end", values=values)
            
            # Ajouter la ligne des totaux
            if self.totals_data:
                totals_values = [
                    "TOTAUX",
                    f"{self.totals_data['entre_vivo_ess']:.0f}",
                    f"{self.totals_data['entre_vivo_gas']:.0f}",
                    f"{self.totals_data['balance_prec_ess']:.0f}",
                    f"{self.totals_data['balance_prec_gas']:.0f}",
                    f"{self.totals_data['prise_compte_ess']:.0f}",
                    f"{self.totals_data['prise_compte_gas']:.0f}",
                    f"{self.totals_data['credit_ess']:.0f}",
                    f"{self.totals_data['credit_gas']:.0f}",
                    f"{self.totals_data['sortie_ess']:.0f}",
                    f"{self.totals_data['sortie_gas']:.0f}",
                    f"{self.totals_data['reste_ess']:.0f}",
                    f"{self.totals_data['reste_gas']:.0f}",
                    f"{self.totals_data['balance_mois_ess']:.0f}",
                    f"{self.totals_data['balance_mois_gas']:.0f}",
                    f"{self.totals_data['total_dist_ess']:.0f}",
                    f"{self.totals_data['total_dist_gas']:.0f}"
                ]
                self.tree.insert("", "end", values=totals_values, tags=("totals",))
                self.tree.tag_configure("totals", background="#FFD700", foreground="black")
    
    def run(self):
        """Lance l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    app = DashboardApp()
    app.run()

if __name__ == "__main__":
    main()
