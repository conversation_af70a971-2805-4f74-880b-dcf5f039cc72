# Fonctionnalités de l'Application

## 🎯 **Fonctionnalités Principales**

### 1. **Interface de Sélection des Dépôts (`home.py`)**
- Interface moderne avec 8 boutons de dépôts
- Clic sur un dépôt lance l'application avec l'ID correspondant
- Design responsive avec effets hover

### 2. **Interface Principale (`main_simple.py`)**
- Tableau Excel-like avec colonnes ESSENCE/GASOIL
- Calculs automatiques en temps réel
- Gestion des dates avec calendrier
- Opérations CRUD (Sauvegarder, Charger, Effacer)

### 3. **Graphiques Avancés (`graphs.py`)**
- **4 onglets d'analyse** :
  - 🏢 **Par Unité** : Distribution par unité, comparaisons
  - 📋 **Données Info** : Analyse des informations globales
  - 📈 **Distribution** : État des stocks, balances
  - 📅 **Évolution** : Tendances temporelles, moyennes mobiles

### 4. **Export Excel (`export_excel.py`)**
- Export complet avec formatage professionnel
- Inclut données du tableau ET informations globales
- Nom de fichier automatique avec date et dépôt
- Styles et bordures pour lisibilité

## 📊 **Types de Graphiques Disponibles**

### **Onglet "Par Unité"**
1. **Distribution Essence par Unité** - Barres verticales
2. **Distribution Gasoil par Unité** - Barres verticales  
3. **Comparaison Essence vs Gasoil** - Barres groupées
4. **Répartition par Activité** - Graphique en secteurs

### **Onglet "Données Info"**
- 8 graphiques en barres groupées pour :
  - ENTRE VIVO ENERGY
  - BALANCE MOIS PRECEDENT
  - PRISE EN COMPTE
  - CREDIT UNITES
  - SORTIE DES COMPTES
  - RESTE
  - BALANCE MOIS
  - TOTAL DISTRIBUTIONS

### **Onglet "Distribution"**
1. **État des Stocks Essence** - Graphique en secteurs
2. **État des Stocks Gasoil** - Graphique en secteurs
3. **Balance Mensuelle** - Barres avec seuil zéro
4. **Indicateurs de Performance** - Taux d'efficacité

### **Onglet "Évolution"**
1. **Évolution Consommation Essence** - Ligne temporelle
2. **Évolution Consommation Gasoil** - Ligne temporelle
3. **Comparaison Temporelle** - Lignes superposées
4. **Tendances** - Moyennes mobiles 7 jours

## 📤 **Export Excel**

### **Fonctionnalités d'Export**
- **Fichier unique** : Données d'une date spécifique
- **Formatage professionnel** : Couleurs, bordures, alignements
- **Données complètes** : Tableau + informations globales
- **Nom automatique** : `ETAT_DEPOT_D1_DEPOT_LAAY_20240923.xlsx`

### **Structure du Fichier Excel**
1. **Titre** : Dépôt, nom, date
2. **En-têtes** : Colonnes principales et sous-colonnes ESSENCE/GASOIL
3. **Données du tableau** : Toutes les unités avec valeurs
4. **Section Informations** : Données globales avec calculs

### **Styles Appliqués**
- **En-têtes** : Fond bleu, texte blanc, gras
- **Titre** : Fond jaune, texte noir, taille 16
- **Bordures** : Fines sur toutes les cellules
- **Alignement** : Centre pour textes, droite pour nombres
- **Largeurs** : Ajustées automatiquement

## 🔧 **Calculs Automatiques**

### **Dans le Tableau**
- **Total R.EX accordés** = Somme des exercices accordés
- **Total R.EX perçus** = Somme des exercices perçus  
- **Total distribués** = DM + PF + TG + Total R.EX perçus

### **Dans les Informations**
- **Reste** = ENTRE + PRISE EN COMPTE + BALANCE MOIS PRECEDENT
- **Total distributions finales** = Total distribués + Sorties - Crédits
- **Balance mois** = Reste - Total distributions finales

## 🎨 **Interface Utilisateur**

### **Barre de Titre**
- **ID du dépôt** (ex: D1) - Fond orange
- **Nom complet** (ex: ETAT DU DEPOT - DEPOT LAAY)
- **Boutons fonctionnels** :
  - 📊 **GRAPHES** - Fond cyan
  - 📤 **EXPORT EXCEL** - Fond vert

### **Zone de Date**
- Affichage de la date actuelle
- Bouton calendrier pour sélection
- Chargement automatique des données

### **Tableau Principal**
- Interface Excel-like familière
- Colonnes ESSENCE/GASOIL
- Calculs automatiques en vert et gras
- Navigation au clavier (Tab, Entrée)

### **Boutons CRUD**
- 💾 **Sauvegarder** - Fond vert
- 🔁 **Charger** - Fond bleu  
- 🗑️ **Effacer Tout** - Fond rouge

### **Zone d'Informations**
- 8 catégories avec sous-colonnes ESSENCE/GASOIL
- Calculs automatiques des balances
- Couleurs alternées pour lisibilité

## 📁 **Architecture des Fichiers**

```
📁 Projet/
├── 📄 home.py              # Interface de sélection des dépôts
├── 📄 main.py              # Point d'entrée principal
├── 📄 main_simple.py       # Interface principale avec tableau
├── 📄 db_utils.py          # Utilitaires de base de données
├── 📄 config.py            # Configuration
├── 📄 graphs.py            # Module des graphiques
├── 📄 export_excel.py      # Module d'export Excel
├── 📄 requirements.txt     # Dépendances Python
├── 📄 run.bat             # Script Windows
├── 📄 run.sh              # Script Linux/Mac
└── 📄 README.md           # Documentation
```

## 🚀 **Utilisation**

### **Lancement**
```bash
# Interface de sélection
python home.py

# Lancement direct
python main.py D1
```

### **Navigation**
1. **Sélectionner un dépôt** dans `home.py`
2. **Choisir une date** avec le calendrier
3. **Saisir les données** dans le tableau
4. **Visualiser les graphiques** avec le bouton 📊
5. **Exporter en Excel** avec le bouton 📤

### **Workflow Recommandé**
1. Ouvrir l'application avec `home.py`
2. Sélectionner le dépôt souhaité
3. Choisir la date de saisie
4. Remplir le tableau avec les données
5. Vérifier les calculs automatiques
6. Sauvegarder les données
7. Analyser avec les graphiques
8. Exporter pour archivage

## 💡 **Conseils d'Utilisation**

### **Pour les Graphiques**
- Utilisez l'onglet "Évolution" pour voir les tendances
- L'onglet "Distribution" montre l'état des stocks
- Les graphiques se mettent à jour automatiquement

### **Pour l'Export Excel**
- Le fichier inclut toutes les données de la date sélectionnée
- Le formatage est optimisé pour l'impression
- Les noms de fichiers sont automatiques et descriptifs

### **Pour les Données**
- Les calculs se font en temps réel
- Sauvegardez régulièrement vos données
- Chaque dépôt a ses données séparées
